from maix import image, display, app, time, camera
import cv2
import numpy as np

disp = display.Display()
cam = camera.Camera(320, 240, image.Format.FMT_BGR888)

# 定义矩形过滤参数
min_area = 5000  # 最小矩形面积
max_area = 40000  # 最大矩形面积
min_aspect_ratio = 0.2  # 最小长宽比
max_aspect_ratio = 5  # 最大长宽比
corner_threshold = 8  # 角点位置接近的阈值

# 新增变量用于记录内外框检测结果
rect_detection_results = []
DETECTION_TIMES = 10
POSITION_CHANGE_THRESHOLD = 10  # 位置变化阈值
middle_rect_points = []  # 中间矩形的点

def detect_rectangles(img):
    # 转换为灰度图像
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 高斯平滑滤波
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # 调整边缘检测参数
    edged = cv2.Canny(blurred, 50, 150)

    # 定义膨胀核
    kernel = np.ones((3, 3), np.uint8)
    # 对边缘图像进行膨胀操作
    dilated_edges = cv2.dilate(edged, kernel, iterations=1)

    # 查找轮廓，使用 RETR_TREE 模式来检测内外轮廓
    contours, hierarchy = cv2.findContours(dilated_edges.copy(), cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

    # 矩形检测
    rectangles = []
    for i, contour in enumerate(contours):
        perimeter = cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, 0.04 * perimeter, True)
        if len(approx) == 4:
            # 计算矩形的面积
            area = cv2.contourArea(approx)
            # 计算矩形的边界框
            x, y, w, h = cv2.boundingRect(approx)
            # 计算长宽比
            aspect_ratio = float(w) / h if h != 0 else 0
            # 过滤不符合条件的矩形
            if min_area < area < max_area and min_aspect_ratio < aspect_ratio < max_aspect_ratio:
                rectangles.append(approx)

    # 根据角点位置合并接近的矩形
    merged_rectangles = []
    for rect in rectangles:
        found_match = False
        for i, merged_rect in enumerate(merged_rectangles):
            corner_matches = 0
            for corner1 in rect:
                for corner2 in merged_rect:
                    distance = np.linalg.norm(np.array(corner1[0]) - np.array(corner2[0]))
                    if distance < corner_threshold:
                        corner_matches += 1
                        break
            if corner_matches >= 3:
                found_match = True
                if cv2.contourArea(rect) > cv2.contourArea(merged_rect):
                    merged_rectangles[i] = rect
                break
        if not found_match:
            merged_rectangles.append(rect)

    # 对合并后的矩形按面积排序
    merged_rectangles.sort(key=lambda r: cv2.contourArea(r), reverse=True)

    # 进一步过滤，只保留最大的两个矩形
    if len(merged_rectangles) > 2:
        merged_rectangles = merged_rectangles[:2]

    return merged_rectangles

def is_position_stable(results):
    if len(results) < DETECTION_TIMES:
        return False
    outer_rects = [result[0] for result in results]
    inner_rects = [result[1] for result in results]

    outer_corners = [rect.flatten() for rect in outer_rects]
    inner_corners = [rect.flatten() for rect in inner_rects]

    outer_std = np.std(outer_corners, axis=0)
    inner_std = np.std(inner_corners, axis=0)

    return np.all(outer_std < POSITION_CHANGE_THRESHOLD) and np.all(inner_std < POSITION_CHANGE_THRESHOLD)

def draw_middle_rect(outer_rect, inner_rect, img):
    outer_points = outer_rect.reshape(-1, 2)
    inner_points = inner_rect.reshape(-1, 2)

    middle_points = []
    for i in range(4):
        mid_point = ((outer_points[i][0] + inner_points[i][0]) // 2, (outer_points[i][1] + inner_points[i][1]) // 2)
        middle_points.append(mid_point)

    # 计算中点
    for i in range(4):
        next_i = (i + 1) % 4
        mid_edge_point = ((middle_points[i][0] + middle_points[next_i][0]) // 2, (middle_points[i][1] + middle_points[next_i][1]) // 2)
        middle_points.append(mid_edge_point)

    global middle_rect_points
    middle_rect_points = middle_points

    # 绘制中间矩形
    for i in range(4):
        next_i = (i + 1) % 4
        cv2.line(img, middle_points[i], middle_points[next_i], (255, 0, 0), 2)

    # 绘制角点和中点
    for point in middle_points:
        cv2.circle(img, point, 5, (255, 0, 0), -1)

    return img

def detect_lasers(img):
    Green_laser_coords = None
    Red_laser_coords = None

    # 转换颜色空间为 HSV
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

    # 定义激光点颜色范围
    lower_red1 = np.array([0, 100, 50])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 100, 50])
    upper_red2 = np.array([180, 255, 255])
    lower_green = np.array([40, 100, 50])
    upper_green = np.array([80, 255, 255])

    # 创建红色和绿色激光的二值化图像
    mask_red1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask_red2 = cv2.inRange(hsv, lower_red2, upper_red2)
    mask_red = cv2.bitwise_or(mask_red1, mask_red2)
    mask_green = cv2.inRange(hsv, lower_green, upper_green)

    # 闭运算
    kernel = np.ones((5, 5), np.uint8)
    mask_red = cv2.morphologyEx(mask_red, cv2.MORPH_CLOSE, kernel)
    mask_green = cv2.morphologyEx(mask_green, cv2.MORPH_CLOSE, kernel)

    # 寻找红色激光外轮廓
    contours_red, _ = cv2.findContours(mask_red, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in contours_red:
        # 找到最小矩形框
        rect = cv2.minAreaRect(contour)
        # 直接从元组中提取中心坐标
        laser_coords = tuple(map(int, rect[0]))
        r_sum, g_sum = get_pixel_sum(img, laser_coords)
        if r_sum > g_sum:
            Red_laser_coords = laser_coords
            cv2.circle(img, laser_coords, 5, (0, 0, 255), -1)
            cv2.putText(img, "Red Laser", (laser_coords[0], laser_coords[1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

    # 寻找绿色激光外轮廓
    contours_green, _ = cv2.findContours(mask_green, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in contours_green:
        # 找到最小矩形框
        rect = cv2.minAreaRect(contour)
        # 直接从元组中提取中心坐标
        laser_coords = tuple(map(int, rect[0]))
        r_sum, g_sum = get_pixel_sum(img, laser_coords)
        if g_sum > r_sum:
            Green_laser_coords = laser_coords
            cv2.circle(img, laser_coords, 5, (0, 255, 0), -1)
            cv2.putText(img, "Green Laser", (laser_coords[0], laser_coords[1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

    return Red_laser_coords, Green_laser_coords

def get_pixel_sum(image, coords):
    # 获取图像宽度和高度
    height, width = image.shape[:2]
    radius = 3
    # 确定方圆的左上角和右下角坐标
    x, y = coords
    x_start = max(0, x - radius)
    y_start = max(0, y - radius)
    x_end = min(width - 1, x + radius)
    y_end = min(height - 1, y + radius)

    # 提取方圆区域
    roi = image[y_start:y_end, x_start:x_end]

    # 计算 R 和 G 通道总值
    # 选取红色
    r_channel = roi[:, :, 2]
    # 绿色
    g_channel = roi[:, :, 1]
    # 求和
    r_sum = int(r_channel.sum())
    g_sum = int(g_channel.sum())

    return r_sum, g_sum

rect_detection_finished = False

while not app.need_exit():
    img = cam.read()
    # convert maix.image.Image object to numpy.ndarray object
    t = time.ticks_ms()
    img = image.image2cv(img, ensure_bgr=False, copy=False)
    print("time: ", time.ticks_ms() - t)

    if not rect_detection_finished:
        merged_rectangles = detect_rectangles(img)

        if len(merged_rectangles) == 2:
            rect_detection_results.append(merged_rectangles)

            if is_position_stable(rect_detection_results):
                outer_rect = merged_rectangles[0]
                inner_rect = merged_rectangles[1]
                img = draw_middle_rect(outer_rect, inner_rect, img)
                print("Middle rectangle points:", middle_rect_points)
                rect_detection_finished = True
            else:
                outer_rect = merged_rectangles[0]
                inner_rect = merged_rectangles[1]
                outer_color = (0, 0, 255)  # 外框红色
                inner_color = (0, 255, 0)  # 内框绿色
                cv2.drawContours(img, [outer_rect], -1, outer_color, 2)
                cv2.putText(img, "Outer Frame", (outer_rect[0][0][0], outer_rect[0][0][1] - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, outer_color, 2)
                cv2.drawContours(img, [inner_rect], -1, inner_color, 2)
                cv2.putText(img, "Inner Frame", (inner_rect[0][0][0], inner_rect[0][0][1] - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, inner_color, 2)
        elif len(merged_rectangles) == 1:
            rect = merged_rectangles[0]
            if cv2.contourArea(rect) > (max_area + min_area) / 2:
                color = (0, 0, 255)
                label = "Outer Frame"
            else:
                color = (0, 255, 0)
                label = "Inner Frame"
            cv2.drawContours(img, [rect], -1, color, 2)
            cv2.putText(img, label, (rect[0][0][0], rect[0][0][1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    else:
        # 先进行激光识别
        red_laser_coords, green_laser_coords = detect_lasers(img)
        if red_laser_coords:
            print("Red laser coordinates:", red_laser_coords)
        if green_laser_coords:
            print("Green laser coordinates:", green_laser_coords)

        # 再绘制中间矩形的点
        for point in middle_rect_points:
            cv2.circle(img, point, 5, (255, 0, 0), -1)

    # show by maix.display
    img_show = image.cv2image(img, bgr=True, copy=False)
    disp.show(img_show)