## 多功能调参器介绍

![image-20250421154737074](激光.assets/image-20250421154737074.png)

本软件使用python进行打包 双击直接运行  

代码本身由pyqt编写 也可直接运行image_processor.py 需按照以下依赖

pip install opencv-python==********
pip install numpy==1.24.3
pip install PyQt5==5.15.9

### 主界面

<img src="激光.assets/image-20250421155138274.png" alt="image-20250421155138274" style="zoom: 33%;" />



四种模式选择 HSV 灰度 还有RGB 和红色模式 （此模式是 HSV识别红色时调节参数使用）

可以帮助更好的了解色彩空间和二值化相关知识

还可以使用鼠标在图像里面框选ROI区域来进行指定区域进行处理

点击保存阈值后会在同路径下生成阈值txt文件 方便复制粘贴



## 项目概述

该代码实现了一个激光检测系统，能够通过摄像头采集视频流，处理每一帧图像并识别激光颜色。通过边缘检测、轮廓检测等方法，系统能够识别红色和绿色激光，并绘制外接矩形框、中心点十字等信息。

## 步骤说明

### 1. 加载参数 (`load_parameters`)

在程序开始时，我们加载一些重要的全局参数。可以从文件 `thresholds_灰度.txt` 中读取设置，例如灰度值的阈值、曝光值和感兴趣区域（ROI）。如果文件不存在或读取失败，将使用默认参数。

#### 关键参数：

- `GRAY_MIN`: 最小灰度值。
- `GRAY_MAX`: 最大灰度值。
- `EXPOSURE`: 曝光值。
- `ROI`: 目标区域，决定摄像头的处理范围。

**效果图**: 无直接效果图，此步骤主要是加载配置文件。

------

### 2. 打开摄像头并设置曝光 (`main`)

使用 `cv2.VideoCapture(0)` 打开摄像头，并设置曝光值为指定值（默认为 -7）。曝光值的设置有助于在不同的光照条件下获取更好的图像效果。

**效果图**: 无直接效果图，此步骤主要是设置摄像头。

------

### 3. 应用感兴趣区域（ROI）

根据预设的 ROI 参数，我们提取图像的指定区域进行进一步处理。这样可以避免处理图像中的不相关部分，从而提高处理效率。

#### 操作步骤：

- 提取图像区域：通过 `ROI = [x1, y1, x2, y2]` 指定图像区域。
- 绘制矩形框显示该区域：`cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)`

**效果图**: 显示原始图像并用绿色矩形框标出感兴趣区域（ROI）。

------

### 4. 灰度图处理

通过将 ROI 区域或整个图像转换为灰度图，进一步进行二值化处理。二值化后的图像便于后续的边缘检测和轮廓分析。

#### 处理步骤：

- 转换为灰度图：`gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)`
- 应用灰度阈值：`binary = cv2.inRange(gray, GRAY_MIN, GRAY_MAX)`

**效果图**: 显示二值化图像（黑白图像），其中激光部分为白色，背景为黑色。

------

### 5. 边缘检测 (`Canny`)

在二值化图像的基础上，使用 `cv2.Canny` 进行边缘检测，便于后续的轮廓提取。

#### 处理步骤：

- 使用 Canny 边缘检测：`edges = cv2.Canny(binary, 50, 150)`

**效果图**: 显示边缘检测后的图像，轮廓部分为白色，背景为黑色。

------

### 6. 轮廓检测

通过 `cv2.findContours` 方法从二值化图像中提取轮廓。每个轮廓代表一个可能的激光区域。

#### 处理步骤：

- 查找轮廓：`contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)`
- 绘制轮廓：`cv2.drawContours(contour_result, contours, -1, (0, 255, 255), 2)`

**效果图**: 显示提取到的轮廓，轮廓部分为黄色。

------

### 7. 激光颜色分析

通过分析轮廓区域内的红、绿、蓝（RGB）通道的总和，判断该轮廓是红色激光还是绿色激光。

#### 处理步骤：

- 计算每个轮廓区域的红、绿、蓝通道的总和。
- 判断激光类型：如果绿色通道的值较高，则为绿色激光，反之为红色激光。

**效果图**: 显示根据激光颜色判断的矩形框，红色激光用红色框标出，绿色激光用绿色框标出。

------

### 8. 绘制外接矩形和中心十字

在每个检测到的激光轮廓周围绘制外接矩形框，并在矩形中心绘制十字标记。

#### 处理步骤：

- 获取外接矩形：`x, y, w, h = cv2.boundingRect(contour)`
- 绘制矩形：`cv2.rectangle(rectangle_result, (x, y), (x+w, y+h), rect_color, 2)`
- 绘制中心十字：`cv2.drawMarker(rectangle_result, (center_x, center_y), (0, 0, 255), cv2.MARKER_CROSS, 10, 2)`

**效果图**: 显示带有外接矩形和十字标记的图像，中心点用十字标记显示。