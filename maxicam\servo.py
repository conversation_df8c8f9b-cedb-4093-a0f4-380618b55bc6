from maix import uart
import time
class ServoController:
    def __init__(self, device="/dev/ttyS0", baudrate=115200):
        """
        初始化舵机控制器
        Args:
            device: 串口设备名称
            baudrate: 波特率
        """
        self.serial = uart.UART(device, baudrate)
        self.SERVO_OFFSETS = {
            1: 85,  # 舵机1的零位偏移是-90度
            2: 85,  # 舵机2的零位偏移是-85度
            3: 82.5,  # 舵机3的零位偏移是-82.5度
            4: 87.5   # 舵机4的零位偏移是-87.5度
        }

    def angle_to_position(self, servo_id, angle):
        """
        将角度转换为位置值，考虑零位偏移
        Args:
            servo_id: 舵机ID
            angle: 目标角度
        Returns:
            转换后的位置值
        """
        offset = self.SERVO_OFFSETS.get(servo_id, 0)
        return int((angle + offset) / 360 * 4096)

    def bus_servo_control(self, servo_id, position, tim):
        """
        控制舵机位置
        Args:
            servo_id: 舵机ID
            position: 目标位置（96-4096）
            tim: 运动时间
        """
        if position < 96 or position > 4096:
            print("无效的位置值，必须在96到4096之间")
            return

        try:
            # 构建命令格式
            pos_H = (position >> 8) & 0xFF
            pos_L = position & 0xFF
            tim_H = (tim >> 8) & 0xFF
            tim_L = tim & 0xFF
            s_id = servo_id & 0xFF
            length = 0x07
            cmd = 0x03
            addr = 0x2a

            # 计算校验和
            sum_value = (s_id + length + cmd + addr + pos_H + pos_L + tim_H + tim_L) % 256
            checknum = 255 - sum_value

            # 构建数据帧
            data = bytes([
                0xFF, 0xFF, s_id, length, cmd, addr,
                pos_H, pos_L, tim_H, tim_L, checknum
            ])

            # 发送数据
            self.serial.write(data)
        except Exception as e:
            print(f"舵机控制错误: {str(e)}")

    def move_to_angle(self, servo_id, angle, time_ms):
        """
        控制舵机转动到指定角度
        Args:
            servo_id: 舵机ID
            angle: 目标角度
            time_ms: 运动时间（毫秒）
        """
        position = self.angle_to_position(servo_id, angle)
        self.bus_servo_control(servo_id, position, time_ms)

# 使用示例
if __name__ == '__main__':
    # 创建舵机控制器实例
    controller = ServoController()
        
    # 控制舵机1转到90度，用时1秒
    controller.move_to_angle(1, 90, 1000)
        
    # 控制舵机2转到45度，用时500毫秒
    controller.move_to_angle(2, 90, 500)
    # time.sleep(1)
    #     # 控制舵机1转到90度，用时1秒
    # controller.move_to_angle(1, 140, 1000)