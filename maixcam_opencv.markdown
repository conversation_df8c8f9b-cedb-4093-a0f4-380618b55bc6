# maixcam使用Opencv

​		对于 MaixCAM，因为使用了 Linux， 并且性能基本能够支撑使用`Python`版本的`OpenCV`，所以除了使用`maix`模块，你也可以直接使用`cv2`模块。

我们来官网具体感受一下[MaixCAM MaixPy 使用 OpenCV - MaixPy](https://wiki.sipeed.com/maixpy/doc/zh/vision/opencv.html)



maixcam可以使用的镜头

![image-20250503152520288](maixcam_opencv.assets/image-20250503152520288.png)

![image-20250503152531986](maixcam_opencv.assets/image-20250503152531986.png)

<img src="maixcam_opencv.assets/image-20250503152459363.png" alt="image-20250503152459363" style="zoom: 50%;" />

# 转换代码

矩形识别

```
import cv2

# 打开摄像头
cap = cv2.VideoCapture(0)

if not cap.isOpened():
    print("无法打开摄像头，请检查设备连接。")
else:
    # 定义最小轮廓面积
    min_contour_area = 1000
    max_contour_area = 30000

    while True:
        # 读取一帧图像
        ret, frame = cap.read()

        if not ret:
            print("无法读取帧，请检查摄像头。")
            break

        # 定义缩放比例
        scale_percent = 50
        width = int(frame.shape[1] * scale_percent / 100)
        height = int(frame.shape[0] * scale_percent / 100)
        dim = (width, height)

        # 缩放图像
        resized_image = cv2.resize(frame, dim, interpolation=cv2.INTER_AREA)
        # 转换为灰度图像
        gray_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2GRAY)
        # 进行二值化处理
        _, binary_image = cv2.threshold(gray_image, 46, 255, cv2.THRESH_BINARY)

        # 查找轮廓
        contours, _ = cv2.findContours(binary_image, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        # 指定要显示的边数
        target_sides = 4
        approximated_contours = []
        for contour in contours:
            # 计算轮廓面积
            area = cv2.contourArea(contour)
            if area > min_contour_area and area < max_contour_area:
                # 计算轮廓周长
                print("轮廓面积:", area)
                # 进行多边形逼近
                epsilon = 0.03 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                if len(approx) == target_sides:
                    approximated_contours.append((approx, area))

        # 按面积排序，面积大的为外框，面积小的为内框
        approximated_contours.sort(key=lambda x: x[1], reverse=True)

        # 在原始缩放图像上绘制指定边数的轮廓
        contour_image = resized_image.copy()
        for i, (approx, area) in enumerate(approximated_contours):
            if i == 0:  # 最大面积的为外框，用红色绘制
                color = (0, 0, 255)
            else:  # 其他为内框，用绿色绘制
                color = (0, 255, 0)
            cv2.drawContours(contour_image, [approx], -1, color, 2)

        # 显示原始图像
        cv2.imshow('Original Image', resized_image)
        # 显示灰度图像
        cv2.imshow('Grayscale Image', gray_image)
        # 显示二值化图像
        cv2.imshow('Binary Image', binary_image)
        # 显示带有指定边数轮廓的图像
        cv2.imshow('Contour Image with Specified Sides', contour_image)

        # 按 'Esc' 键退出循环
        if cv2.waitKey(1) == 27:
            break

    # 释放摄像头并关闭所有窗口
    cap.release()
    cv2.destroyAllWindows()
    
```

激光识别

```
from maix import image, display, app, time, camera
import cv2

# 初始化显示和摄像头
disp = display.Display()
cam = camera.Camera(320, 240, image.Format.FMT_BGR888)

# 定义参数
min_contour_area = 1000
max_contour_area = 30000
target_sides = 4
scale_percent = 50  # 缩放到原图的百分比

while not app.need_exit():
    # 从摄像头读取图像
    img = cam.read()
    # 将 maix.image.Image 转为 numpy.ndarray（BGR 格式）
    t = time.ticks_ms()
    img = image.image2cv(img, ensure_bgr=False, copy=False)
    print("转换用时(ms):", time.ticks_ms() - t)

    # 缩放图像
    width = int(img.shape[1] * scale_percent / 100)
    height = int(img.shape[0] * scale_percent / 100)
    resized = cv2.resize(img, (width, height), interpolation=cv2.INTER_AREA)

    # 转灰度并二值化
    gray = cv2.cvtColor(resized, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 46, 255, cv2.THRESH_BINARY)

    # 查找轮廓
    contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
    quads = []
    for cnt in contours:
        area = cv2.contourArea(cnt)
        if min_contour_area < area < max_contour_area:
            epsilon = 0.03 * cv2.arcLength(cnt, True)
            approx = cv2.approxPolyDP(cnt, epsilon, True)
            if len(approx) == target_sides:
                quads.append((approx, area))

    # 按面积降序排列：第一个为最大外框，其余为内框
    quads.sort(key=lambda x: x[1], reverse=True)

    # 绘制轮廓
    output = resized.copy()
    for i, (approx, area) in enumerate(quads):
        color = (0, 0, 255) if i == 0 else (0, 255, 0)
        cv2.drawContours(output, [approx], -1, color, 2)

    # 转回 maix.image.Image 并显示
    img_show = image.cv2image(output, bgr=True, copy=False)
    disp.show(img_show)

```

