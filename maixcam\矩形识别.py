from maix import image, display, app, time, camera
import cv2
import numpy as np

disp = display.Display()
cam = camera.Camera(320, 240, image.Format.FMT_BGR888)

# 定义矩形过滤参数
min_area = 5000  # 最小矩形面积
max_area = 40000  # 最大矩形面积
min_aspect_ratio = 0.2  # 最小长宽比
max_aspect_ratio = 5  # 最大长宽比
corner_threshold = 8  # 角点位置接近的阈值

while not app.need_exit():
    img = cam.read()
    # convert maix.image.Image object to numpy.ndarray object
    t = time.ticks_ms()
    img = image.image2cv(img, ensure_bgr=False, copy=False)
    print("time: ", time.ticks_ms() - t)

    # 转换为灰度图像
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 高斯平滑滤波
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # 调整边缘检测参数
    edged = cv2.Canny(blurred, 50, 150)

    # 定义膨胀核
    kernel = np.ones((3, 3), np.uint8)
    # 对边缘图像进行膨胀操作
    dilated_edges = cv2.dilate(edged, kernel, iterations=1)

    # 查找轮廓，使用 RETR_TREE 模式来检测内外轮廓
    contours, hierarchy = cv2.findContours(dilated_edges.copy(), cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

    # 矩形检测
    rectangles = []
    for i, contour in enumerate(contours):
        perimeter = cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, 0.04 * perimeter, True)
        if len(approx) == 4:
            # 计算矩形的面积
            area = cv2.contourArea(approx)
            # 计算矩形的边界框
            x, y, w, h = cv2.boundingRect(approx)
            # 计算长宽比
            aspect_ratio = float(w) / h if h != 0 else 0
            # 过滤不符合条件的矩形
            if min_area < area < max_area and min_aspect_ratio < aspect_ratio < max_aspect_ratio:
                rectangles.append(approx)

    # 根据角点位置合并接近的矩形
    merged_rectangles = []
    for rect in rectangles:
        found_match = False
        for i, merged_rect in enumerate(merged_rectangles):
            corner_matches = 0
            for corner1 in rect:
                for corner2 in merged_rect:
                    distance = np.linalg.norm(np.array(corner1[0]) - np.array(corner2[0]))
                    if distance < corner_threshold:
                        corner_matches += 1
                        break
            if corner_matches >= 3:
                found_match = True
                if cv2.contourArea(rect) > cv2.contourArea(merged_rect):
                    merged_rectangles[i] = rect
                break
        if not found_match:
            merged_rectangles.append(rect)

    # 对合并后的矩形按面积排序
    merged_rectangles.sort(key=lambda r: cv2.contourArea(r), reverse=True)

    # 进一步过滤，只保留最大的两个矩形
    if len(merged_rectangles) > 2:
        merged_rectangles = merged_rectangles[:2]

    # 区分内外框并绘制
    if len(merged_rectangles) == 2:
        outer_rect = merged_rectangles[0]
        inner_rect = merged_rectangles[1]
        outer_color = (0, 0, 255)  # 外框红色
        inner_color = (0, 255, 0)  # 内框绿色
        cv2.drawContours(img, [outer_rect], -1, outer_color, 2)
        cv2.putText(img, "Outer Frame", (outer_rect[0][0][0], outer_rect[0][0][1] - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, outer_color, 2)
        cv2.drawContours(img, [inner_rect], -1, inner_color, 2)
        cv2.putText(img, "Inner Frame", (inner_rect[0][0][0], inner_rect[0][0][1] - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, inner_color, 2)
    elif len(merged_rectangles) == 1:
        rect = merged_rectangles[0]
        if cv2.contourArea(rect) > (max_area + min_area) / 2:
            color = (0, 0, 255)
            label = "Outer Frame"
        else:
            color = (0, 255, 0)
            label = "Inner Frame"
        cv2.drawContours(img, [rect], -1, color, 2)
        cv2.putText(img, label, (rect[0][0][0], rect[0][0][1] - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

    # show by maix.display
    img_show = image.cv2image(img, bgr=True, copy=False)
    disp.show(img_show)
    