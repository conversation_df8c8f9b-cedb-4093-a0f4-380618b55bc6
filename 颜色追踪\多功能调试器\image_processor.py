import sys
import cv2
import numpy as np
import traceback
from PyQt5.QtWidgets import (QApplication, QMainWindow, QLabel, QVBoxLayout, 
                           QHBoxLayout, QWidget, QPushButton, QSlider, 
                           QComboBox, QLineEdit, QMessageBox, QStackedWidget)
from PyQt5.QtCore import Qt, QTimer, QPoint, QDateTime, QRect
from PyQt5.QtGui import QImage, QPixmap, QPainter, QPen, QColor

# 默认阈值
default_lower_blue = np.array([100, 150, 0])
default_upper_blue = np.array([140, 255, 255])
default_roi = [250, 185, 450, 320]  # x1, y1, x2, y2

# 读取阈值和ROI
def read_thresholds(mode):
    """读取阈值和曝光度"""
    try:
        with open(f"thresholds_{mode}.txt", "r") as f:
            lines = f.readlines()
            if mode == "HSV":
                lower = np.array([int(x) for x in lines[0].strip().split(",")])
                upper = np.array([int(x) for x in lines[1].strip().split(",")])
                roi = [int(x) for x in lines[2].strip().split(",")]
                # 检查是否有曝光度行
                exposure = int(lines[3].strip()) if len(lines) > 3 else 0
            elif mode == "灰度":
                lower = int(lines[0].strip())
                upper = int(lines[1].strip())
                roi = [int(x) for x in lines[2].strip().split(",")]
                # 检查是否有曝光度行
                exposure = int(lines[3].strip()) if len(lines) > 3 else 0
            elif mode == "RGB":
                lower = np.array([int(x) for x in lines[0].strip().split(",")])
                upper = np.array([int(x) for x in lines[1].strip().split(",")])
                roi = [int(x) for x in lines[2].strip().split(",")]
                # 检查是否有曝光度行
                exposure = int(lines[3].strip()) if len(lines) > 3 else 0
            return lower, upper, roi, exposure
    except FileNotFoundError:
        print(f"阈值文件不存在: thresholds_{mode}.txt")
        return None, None, None, 0

# 保存阈值和ROI
def save_thresholds(mode, lower, upper, roi, exposure):
    """保存阈值和曝光度"""
    with open(f"thresholds_{mode}.txt", "w") as f:
        if mode == "HSV" or mode == "RGB":
            f.write(",".join(map(str, lower)) + "\n")
            f.write(",".join(map(str, upper)) + "\n")
        elif mode == "灰度":
            f.write(f"{lower}\n")
            f.write(f"{upper}\n")
        f.write(",".join(map(str, roi)) + "\n")
        f.write(f"{exposure}\n")
    print(f"{mode}模式的阈值和曝光度已保存")

class ImageProcessor(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图像处理器")
        self.setGeometry(100, 100, 1600, 800)

        # 初始化变量
        self.mode = "HSV"  # 默认模式
        self.is_inverted = False
        self.roi = None
        self.roi_coords = None
        self.camera_index = 1  # 初始化摄像头索引
        self.camera_opened = False  # 摄像头状态
        self.capture = None  # 初始化capture变量
        self.last_frame_time = 0  # 用于帧率控制
        self.frame_interval = 30  # 帧间隔(ms)
        self.is_switching = False  # 标记是否正在切换摄像头
        self.fail_count = 0  # 初始化失败计数
        
        # ROI选择相关变量
        self.drawing = False
        self.start_point = None
        self.end_point = None
        self.current_frame = None
        self.frame_size = None
        self.display_size = None  # 显示区域大小
        self.cached_pixmap = None  # 用于绘制ROI的缓存图像

        # 创建UI组件
        self.create_ui()
        
        # 设置定时器更新图像
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_frame)
        self.timer.start(self.frame_interval)  # 30ms更新一次
        
        # 设置摄像头状态检查定时器
        self.camera_check_timer = QTimer()
        self.camera_check_timer.timeout.connect(self.check_camera_status)
        self.camera_check_timer.start(5000)  # 每5秒检查一次摄像头状态

        # 读取所有模式的默认阈值
        self.set_default_thresholds()

    def init_camera(self):
        """初始化摄像头"""
        try:
            # 先关闭当前摄像头
            self.close_camera()
            
            # 延迟一小段时间，确保资源完全释放
            QTimer.singleShot(100, lambda: self._init_camera_after_delay())
        except Exception as e:
            print(f"初始化摄像头时出错: {str(e)}")
            return False

    def _init_camera_after_delay(self):
        """初始化摄像头"""
        try:
            # 根据测试结果，摄像头1使用默认后端就能正常工作
            print(f"正在初始化摄像头 {self.camera_index}")
            self.capture = cv2.VideoCapture(self.camera_index)

            # 如果无法打开摄像头，尝试使用DirectShow后端
            if not self.capture.isOpened():
                print(f"尝试使用DirectShow后端打开摄像头 {self.camera_index}")
                self.capture = cv2.VideoCapture(self.camera_index, cv2.CAP_DSHOW)

            # 再次检查是否打开成功
            if not self.capture.isOpened():
                raise Exception(f"无法打开摄像头 {self.camera_index}")
            
            # 设置摄像头参数
            self.capture.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.capture.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.capture.set(cv2.CAP_PROP_FPS, 30)
            
            # 检查摄像头设置是否成功
            actual_width = self.capture.get(cv2.CAP_PROP_FRAME_WIDTH)
            actual_height = self.capture.get(cv2.CAP_PROP_FRAME_HEIGHT)
            print(f"摄像头实际分辨率: {actual_width}x{actual_height}")
            
            # 设置曝光度 - 仅在支持时设置
            try:
                self.capture.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0)  # 尝试关闭自动曝光
                exposure_value = self.exposure_slider.value()
                self.capture.set(cv2.CAP_PROP_EXPOSURE, exposure_value)
                print(f"设置曝光度为: {exposure_value}")
            except Exception as exp_err:
                print(f"设置曝光度时出错: {str(exp_err)}")
                print("继续使用摄像头默认曝光设置")
            
            # 读取一帧测试 - 对于摄像头1，即使读取失败也继续
            ret, test_frame = self.capture.read()
            if not ret or test_frame is None:
                if self.camera_index == 1:
                    print(f"摄像头1读取测试帧失败，但继续初始化（可能在实际使用时正常）")
                else:
                    raise Exception("无法从摄像头读取图像")
                
            self.camera_opened = True
            self.camera_button.setText("关闭摄像头")
            print(f"成功初始化摄像头 {self.camera_index}")
            return True
        except Exception as e:
            print(f"初始化摄像头时出错: {str(e)}")
            self.close_camera()
            QMessageBox.warning(self, "错误", f"初始化摄像头失败: {str(e)}")
            return False

    def close_camera(self):
        """关闭摄像头"""
        try:
            if self.capture is not None:
                self.capture.release()
                # 等待释放完成
                import time
                time.sleep(0.1)
                
            self.capture = None
            self.camera_opened = False
            self.camera_button.setText("打开摄像头")
            # 清空图像显示
            self.original_label.clear()
            self.processed_label.clear()
            print("摄像头已关闭")
        except Exception as e:
            print(f"关闭摄像头时出错: {str(e)}")
            # 强制释放
            self.capture = None
            self.camera_opened = False

    def toggle_camera(self):
        """切换摄像头开关状态"""
        if self.camera_opened:
            self.close_camera()
        else:
            if self.init_camera():
                self.update_frame()

    def refresh_cameras(self):
        """刷新摄像头列表"""
        try:
            self.camera_combo.clear()
            available_cameras = []
            
            # 先关闭当前摄像头
            if self.camera_opened:
                self.close_camera()
                # 等待资源释放
                QApplication.processEvents()
                import time
                time.sleep(0.5)  # 等待系统完全释放摄像头资源
            
            # 检查前10个摄像头，从0开始
            for i in range(10):
                cap = None
                try:
                    print(f"正在检查摄像头 {i}...")
                    # 先尝试不使用DirectShow
                    cap = cv2.VideoCapture(i)

                    # 等待短暂时间以确保摄像头初始化
                    QApplication.processEvents()

                    if cap.isOpened():
                        print(f"摄像头 {i} 已打开，尝试读取图像...")
                        ret, frame = cap.read()
                        if ret and frame is not None:  # 确保能读取到图像且图像非空
                            available_cameras.append(i)
                            self.camera_combo.addItem(f"摄像头 {i}")
                            print(f"找到摄像头 {i} - 成功读取图像")
                        else:
                            # 即使无法读取图像，如果摄像头能打开，也将其添加到列表中
                            # 特别是摄像头1，我们希望能够使用它
                            if i == 1:
                                available_cameras.append(i)
                                self.camera_combo.addItem(f"摄像头 {i} (需要特殊初始化)")
                                print(f"找到摄像头 {i} - 打开成功但读取失败，仍添加到列表")
                            else:
                                print(f"摄像头 {i} 打开但无法读取图像")
                    else:
                        print(f"摄像头 {i} 无法打开")
                except Exception as e:
                    print(f"检查摄像头 {i} 时出错: {str(e)}")
                finally:
                    if cap is not None:
                        cap.release()
                        # 释放资源后等待一小段时间
                        QApplication.processEvents()
                        import time
                        time.sleep(0.1)
            
            if available_cameras:
                # 选择当前可用的摄像头
                if self.camera_index not in available_cameras:
                    if available_cameras:  # 确保有可用摄像头
                        # 优先选择摄像头1，如果不可用则选择第一个可用的
                        if 1 in available_cameras:
                            self.camera_index = 1
                            self.camera_combo.setCurrentIndex(available_cameras.index(1))
                        else:
                            self.camera_index = available_cameras[0]
                            self.camera_combo.setCurrentIndex(0)
                else:
                    # 选择匹配当前索引的条目
                    self.camera_combo.setCurrentIndex(available_cameras.index(self.camera_index))
                
                print(f"当前选择的摄像头: {self.camera_index}")
            else:
                QMessageBox.warning(self, "警告", "未检测到可用摄像头")
                self.camera_index = 1
        except Exception as e:
            print(f"刷新摄像头列表时出错: {str(e)}")
            QMessageBox.warning(self, "错误", f"刷新摄像头列表失败: {str(e)}")

    def change_camera(self, index):
        """切换摄像头"""
        if self.is_switching:
            return  # 如果正在切换，直接返回
            
        try:
            if index >= 0:  # 确保索引有效
                self.is_switching = True
                print(f"正在切换到摄像头 {index}")
                
                # 先关闭当前摄像头
                if self.camera_opened:
                    self.close_camera()
                
                # 更新摄像头索引
                self.camera_index = index
                
                # 立即初始化新摄像头
                if self._init_camera_after_delay():
                    self.is_switching = False
        except Exception as e:
            print(f"切换摄像头时出错: {str(e)}")
            self.is_switching = False
            QMessageBox.warning(self, "错误", "切换摄像头失败")

    def create_ui(self):
        # 主布局
        main_layout = QVBoxLayout()

        # 图像显示区域
        image_layout = QHBoxLayout()
        
        # 创建固定大小的容器用于显示原始图像
        self.original_container = QWidget()
        self.original_container.setFixedSize(640, 480)  # 固定显示区域大小
        original_container_layout = QVBoxLayout(self.original_container)
        original_container_layout.setContentsMargins(0, 0, 0, 0)
        self.original_label = QLabel("原始图像")
        self.original_label.setAlignment(Qt.AlignCenter)  # 居中对齐
        self.original_label.setMouseTracking(True)
        
        # 安装事件过滤器代替直接重写鼠标事件方法
        self.original_label.installEventFilter(self)
        
        original_container_layout.addWidget(self.original_label)
        
        # 创建固定大小的容器用于显示处理后的图像
        self.processed_container = QWidget()
        self.processed_container.setFixedSize(640, 480)  # 固定显示区域大小
        processed_container_layout = QVBoxLayout(self.processed_container)
        processed_container_layout.setContentsMargins(0, 0, 0, 0)
        self.processed_label = QLabel("处理后的图像")
        self.processed_label.setAlignment(Qt.AlignCenter)  # 居中对齐
        processed_container_layout.addWidget(self.processed_label)
        
        image_layout.addWidget(self.original_container)
        image_layout.addWidget(self.processed_container)
        main_layout.addLayout(image_layout)

        # 控制面板
        control_layout = QVBoxLayout()

        # 摄像头控制
        camera_control_layout = QHBoxLayout()
        
        # 摄像头选择
        self.camera_combo = QComboBox()
        self.refresh_cameras()
        self.camera_combo.currentIndexChanged.connect(self.change_camera)
        
        refresh_button = QPushButton("刷新摄像头")
        refresh_button.clicked.connect(self.refresh_cameras)
        
        self.camera_button = QPushButton("打开摄像头")
        self.camera_button.clicked.connect(self.toggle_camera)
        
        camera_control_layout.addWidget(QLabel("选择摄像头:"))
        camera_control_layout.addWidget(self.camera_combo)
        camera_control_layout.addWidget(refresh_button)
        camera_control_layout.addWidget(self.camera_button)
        control_layout.addLayout(camera_control_layout)

        # 模式选择按钮
        mode_button_layout = QHBoxLayout()
        self.hsv_button = QPushButton("HSV模式")
        self.gray_button = QPushButton("灰度模式")
        self.rgb_button = QPushButton("RGB模式")
        self.red_button = QPushButton("红色模式")
        
        self.hsv_button.clicked.connect(lambda: self.change_mode("HSV"))
        self.gray_button.clicked.connect(lambda: self.change_mode("灰度"))
        self.rgb_button.clicked.connect(lambda: self.change_mode("RGB"))
        self.red_button.clicked.connect(lambda: self.change_mode("红色"))
        
        mode_button_layout.addWidget(self.hsv_button)
        mode_button_layout.addWidget(self.gray_button)
        mode_button_layout.addWidget(self.rgb_button)
        mode_button_layout.addWidget(self.red_button)
        control_layout.addLayout(mode_button_layout)
        
        # 反转按钮
        self.invert_button = QPushButton("反转")
        self.invert_button.setCheckable(True)
        self.invert_button.clicked.connect(self.toggle_invert)
        control_layout.addWidget(self.invert_button)

        # 曝光度调节
        exposure_layout = QHBoxLayout()
        self.exposure_slider = QSlider(Qt.Horizontal)
        self.exposure_slider.setRange(-13, -1)
        self.exposure_slider.setValue(-7)
        self.exposure_slider.valueChanged.connect(self.update_exposure)
        exposure_layout.addWidget(QLabel("曝光度:"))
        exposure_layout.addWidget(self.exposure_slider)
        control_layout.addLayout(exposure_layout)

        # 创建模式子界面
        self.mode_stack = QStackedWidget()
        
        # HSV模式界面
        hsv_widget = QWidget()
        hsv_layout = QVBoxLayout(hsv_widget)
        self.h_min_slider, h_min_layout = self.create_slider("H最小值", 0, 179)
        self.h_max_slider, h_max_layout = self.create_slider("H最大值", 0, 179)
        self.s_min_slider, s_min_layout = self.create_slider("S最小值", 0, 255)
        self.s_max_slider, s_max_layout = self.create_slider("S最大值", 0, 255)
        self.v_min_slider, v_min_layout = self.create_slider("V最小值", 0, 255)
        self.v_max_slider, v_max_layout = self.create_slider("V最大值", 0, 255)
        
        # 设置HSV滑块的默认值
        self.h_min_slider.setValue(0)
        self.h_max_slider.setValue(179)
        self.s_min_slider.setValue(0)
        self.s_max_slider.setValue(255)
        self.v_min_slider.setValue(0)
        self.v_max_slider.setValue(255)
        
        hsv_layout.addLayout(h_min_layout)
        hsv_layout.addLayout(h_max_layout)
        hsv_layout.addLayout(s_min_layout)
        hsv_layout.addLayout(s_max_layout)
        hsv_layout.addLayout(v_min_layout)
        hsv_layout.addLayout(v_max_layout)
        
        # 添加重置按钮
        hsv_reset_button = QPushButton("重置HSV阈值")
        hsv_reset_button.clicked.connect(self.reset_hsv_thresholds)
        hsv_layout.addWidget(hsv_reset_button)
        
        self.mode_stack.addWidget(hsv_widget)
        
        # 灰度模式界面
        gray_widget = QWidget()
        gray_layout = QVBoxLayout(gray_widget)
        self.gray_min_slider, gray_min_layout = self.create_slider("灰度最小值", 0, 255)
        self.gray_max_slider, gray_max_layout = self.create_slider("灰度最大值", 0, 255)
        
        gray_layout.addLayout(gray_min_layout)
        gray_layout.addLayout(gray_max_layout)
        
        # 添加重置按钮
        gray_reset_button = QPushButton("重置灰度阈值")
        gray_reset_button.clicked.connect(self.reset_gray_thresholds)
        gray_layout.addWidget(gray_reset_button)
        
        self.mode_stack.addWidget(gray_widget)
        
        # RGB模式界面
        rgb_widget = QWidget()
        rgb_layout = QVBoxLayout(rgb_widget)
        self.r_min_slider, r_min_layout = self.create_slider("R最小值", 0, 255)
        self.r_max_slider, r_max_layout = self.create_slider("R最大值", 0, 255)
        self.g_min_slider, g_min_layout = self.create_slider("G最小值", 0, 255)
        self.g_max_slider, g_max_layout = self.create_slider("G最大值", 0, 255)
        self.b_min_slider, b_min_layout = self.create_slider("B最小值", 0, 255)
        self.b_max_slider, b_max_layout = self.create_slider("B最大值", 0, 255)
        
        rgb_layout.addLayout(r_min_layout)
        rgb_layout.addLayout(r_max_layout)
        rgb_layout.addLayout(g_min_layout)
        rgb_layout.addLayout(g_max_layout)
        rgb_layout.addLayout(b_min_layout)
        rgb_layout.addLayout(b_max_layout)
        
        # 添加重置按钮
        rgb_reset_button = QPushButton("重置RGB阈值")
        rgb_reset_button.clicked.connect(self.reset_rgb_thresholds)
        rgb_layout.addWidget(rgb_reset_button)
        
        self.mode_stack.addWidget(rgb_widget)
        
        # 红色模式界面
        red_widget = QWidget()
        red_layout = QVBoxLayout(red_widget)
        
        # H1通道调节
        self.red_h_min1_slider, red_h_min1_layout = self.create_slider("H1最小值", 0, 179)
        self.red_h_max1_slider, red_h_max1_layout = self.create_slider("H1最大值", 0, 179)
        
        # H2通道调节
        self.red_h_min2_slider, red_h_min2_layout = self.create_slider("H2最小值", 0, 179)
        self.red_h_max2_slider, red_h_max2_layout = self.create_slider("H2最大值", 0, 179)
        
        # S通道调节
        self.red_s_min_slider, red_s_min_layout = self.create_slider("S最小值", 0, 255)
        self.red_s_max_slider, red_s_max_layout = self.create_slider("S最大值", 0, 255)
        
        # V通道调节
        self.red_v_min_slider, red_v_min_layout = self.create_slider("V最小值", 0, 255)
        self.red_v_max_slider, red_v_max_layout = self.create_slider("V最大值", 0, 255)
        
        # 设置默认值
        self.red_h_min1_slider.setValue(0)
        self.red_h_max1_slider.setValue(179)  # 修改为最大值
        self.red_h_min2_slider.setValue(0)
        self.red_h_max2_slider.setValue(179)  # 修改为最大值
        self.red_s_min_slider.setValue(0)
        self.red_s_max_slider.setValue(255)
        self.red_v_min_slider.setValue(0)
        self.red_v_max_slider.setValue(255)
        
        red_layout.addLayout(red_h_min1_layout)
        red_layout.addLayout(red_h_max1_layout)
        red_layout.addLayout(red_h_min2_layout)
        red_layout.addLayout(red_h_max2_layout)
        red_layout.addLayout(red_s_min_layout)
        red_layout.addLayout(red_s_max_layout)
        red_layout.addLayout(red_v_min_layout)
        red_layout.addLayout(red_v_max_layout)
        
        # 添加重置按钮
        red_reset_button = QPushButton("重置红色阈值")
        red_reset_button.clicked.connect(self.reset_red_thresholds)
        red_layout.addWidget(red_reset_button)
        
        self.mode_stack.addWidget(red_widget)
        
        control_layout.addWidget(self.mode_stack)

        # 保存按钮
        save_layout = QHBoxLayout()
        self.save_button = QPushButton("保存阈值")
        self.save_button.clicked.connect(self.save_thresholds)
        save_layout.addWidget(self.save_button)
        control_layout.addLayout(save_layout)

        main_layout.addLayout(control_layout)

        # 设置主窗口
        container = QWidget()
        container.setLayout(main_layout)
        self.setCentralWidget(container)

    def create_slider(self, label, min_val, max_val):
        layout = QHBoxLayout()
        slider = QSlider(Qt.Horizontal)
        slider.setRange(min_val, max_val)
        slider.setValue((min_val + max_val) // 2)
        slider.valueChanged.connect(self.update_frame)
        
        # 添加数值显示标签
        value_label = QLabel(str(slider.value()))
        slider.valueChanged.connect(lambda v: value_label.setText(str(v)))
        
        layout.addWidget(QLabel(f"{label}:"))
        layout.addWidget(slider)
        layout.addWidget(value_label)
        return slider, layout

    def change_mode(self, mode):
        self.mode = mode
        if mode == "HSV":
            self.mode_stack.setCurrentIndex(0)
        elif mode == "灰度":
            self.mode_stack.setCurrentIndex(1)
        elif mode == "RGB":
            self.mode_stack.setCurrentIndex(2)
        elif mode == "红色":
            self.mode_stack.setCurrentIndex(3)
        self.set_default_thresholds()
        self.update_frame()

    def process_image(self, frame):
        # 防止空帧或无效帧
        if frame is None or frame.size == 0:
            print("处理的帧为空或无效")
            # 返回一个黑色图像
            return np.zeros((480, 640, 3), dtype=np.uint8)
            
        # 深拷贝帧以避免修改原始数据
        frame = frame.copy()
        orig_frame = frame.copy()  # 保存一份原始帧用于计算比例
            
        # 处理ROI区域
        roi_image = None
        if self.roi_coords:
            try:
                x1, y1, x2, y2 = self.roi_coords
                # 确保坐标在图像范围内
                height, width = frame.shape[:2]
                x1 = max(0, min(x1, width-1))
                y1 = max(0, min(y1, height-1))
                x2 = max(0, min(x2, width-1))
                y2 = max(0, min(y2, height-1))
                
                # 确保roi区域有效
                if x2 <= x1 or y2 <= y1 or x2-x1 < 2 or y2-y1 < 2:
                    print("ROI区域无效，使用原始图像")
                else:
                    # 提取ROI
                    roi = frame[y1:y2, x1:x2]
                    roi_height, roi_width = roi.shape[:2]
                    
                    # 创建一个与原始帧大小相同的白色背景
                    roi_display = np.ones_like(orig_frame) * 255  # 使用白色背景
                    
                    # 计算等比例缩放因子，使ROI尽可能大但保持比例
                    scale_factor = min(width / roi_width, height / roi_height)
                    
                    # 缩放ROI以填充尽可能多的空间
                    new_width = int(roi_width * scale_factor)
                    new_height = int(roi_height * scale_factor)
                    
                    if new_width > 0 and new_height > 0:
                        resized_roi = cv2.resize(roi, (new_width, new_height))
                        
                        # 计算居中位置
                        start_x = (width - new_width) // 2
                        start_y = (height - new_height) // 2
                        
                        # 将缩放后的ROI放在白色背景中央
                        roi_display[start_y:start_y+new_height, start_x:start_x+new_width] = resized_roi
                        
                        # 使用处理后的ROI图像替换原始帧
                        frame = roi_display
                    else:
                        # 如果缩放失败，直接使用ROI
                        frame = roi
            except Exception as e:
                print(f"处理ROI时出错: {str(e)}")
                import traceback
                traceback.print_exc()
                # 出错时使用原始图像继续处理

        # 根据不同模式处理图像
        try:
            if self.mode == "HSV":
                hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
                lower = np.array([self.h_min_slider.value(), self.s_min_slider.value(), self.v_min_slider.value()])
                upper = np.array([self.h_max_slider.value(), self.s_max_slider.value(), self.v_max_slider.value()])
                mask = cv2.inRange(hsv, lower, upper)
                result = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR)
            elif self.mode == "红色":
                hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
                # 第一个H通道区间的HSV阈值
                lower_red1 = np.array([
                    self.red_h_min1_slider.value(),
                    self.red_s_min_slider.value(),
                    self.red_v_min_slider.value()
                ])
                upper_red1 = np.array([
                    self.red_h_max1_slider.value(),
                    self.red_s_max_slider.value(),
                    self.red_v_max_slider.value()
                ])
                # 第二个H通道区间的HSV阈值
                lower_red2 = np.array([
                    self.red_h_min2_slider.value(),
                    self.red_s_min_slider.value(),
                    self.red_v_min_slider.value()
                ])
                upper_red2 = np.array([
                    self.red_h_max2_slider.value(),
                    self.red_s_max_slider.value(),
                    self.red_v_max_slider.value()
                ])
                # 分别对两个H通道区间进行HSV二值化
                mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
                mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
                # 合并两个二值化结果
                mask = cv2.bitwise_or(mask1, mask2)
                result = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR)
            elif self.mode == "灰度":
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                mask = cv2.inRange(gray, self.gray_min_slider.value(), self.gray_max_slider.value())
                result = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR)
            elif self.mode == "RGB":
                b, g, r = cv2.split(frame)
                r_mask = cv2.inRange(r, self.r_min_slider.value(), self.r_max_slider.value())
                g_mask = cv2.inRange(g, self.g_min_slider.value(), self.g_max_slider.value())
                b_mask = cv2.inRange(b, self.b_min_slider.value(), self.b_max_slider.value())
                mask = cv2.bitwise_and(r_mask, g_mask)
                mask = cv2.bitwise_and(mask, b_mask)
                result = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR)
            else:
                # 未知模式，返回原始图像
                result = frame.copy()

            if self.is_inverted:
                result = cv2.bitwise_not(result)

            return result
        except Exception as e:
            print(f"图像处理过程中出错: {str(e)}")
            # 返回原始图像或黑色图像
            try:
                return frame.copy()
            except:
                return np.zeros((480, 640, 3), dtype=np.uint8)

    def update_frame(self):
        """更新图像显示"""
        try:
            current_time = QDateTime.currentMSecsSinceEpoch()
            if current_time - self.last_frame_time < self.frame_interval:
                return  # 控制帧率
                
            self.last_frame_time = current_time
            
            if not self.camera_opened or self.is_switching:
                # 摄像头未打开或正在切换时显示黑色背景
                black_image = np.zeros((480, 640, 3), dtype=np.uint8)
                black_qimage = QImage(black_image.data, 640, 480, 640 * 3, QImage.Format_RGB888)
                self.original_label.setPixmap(QPixmap.fromImage(black_qimage))
                self.processed_label.setPixmap(QPixmap.fromImage(black_qimage))
                return

            if self.capture is None or not self.capture.isOpened():
                print("摄像头未打开或无效")
                self.close_camera()
                return

            # 尝试读取图像
            ret = False
            frame = None
            try_count = 0
            max_tries = 3
            
            while not ret and try_count < max_tries:
                ret, frame = self.capture.read()
                try_count += 1
                if not ret:
                    print(f"读取摄像头失败，尝试 {try_count}/{max_tries}")
                    import time
                    time.sleep(0.1)  # 短暂等待后再次尝试
            
            if not ret or frame is None or frame.size == 0:
                print("无法读取摄像头画面或图像无效")
                # 不立即关闭摄像头，而是显示黑屏
                black_image = np.zeros((480, 640, 3), dtype=np.uint8)
                black_qimage = QImage(black_image.data, 640, 480, 640 * 3, QImage.Format_RGB888)
                self.original_label.setPixmap(QPixmap.fromImage(black_qimage))
                self.processed_label.setPixmap(QPixmap.fromImage(black_qimage))
                
                # 如果连续多次失败，才关闭摄像头
                if hasattr(self, 'fail_count'):
                    self.fail_count += 1
                else:
                    self.fail_count = 1
                    
                if self.fail_count > 10:  # 连续10次失败后关闭摄像头
                    QMessageBox.warning(self, "警告", "摄像头无响应，已自动关闭")
                    self.close_camera()
                    self.fail_count = 0
                return
                
            # 重置失败计数
            if hasattr(self, 'fail_count'):
                self.fail_count = 0

            # 保存当前帧用于绘制
            self.current_frame = frame.copy()
            self.frame_size = (frame.shape[1], frame.shape[0])

            # 处理图像
            processed = self.process_image(frame)

            # 确保处理后的图像有效
            if processed is None or processed.size == 0:
                print("处理后的图像无效")
                processed = np.zeros((480, 640, 3), dtype=np.uint8)

            # 显示原始图像
            try:
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                h, w, ch = frame_rgb.shape
                
                # 计算缩放比例，使图像按比例缩放，但不铺满区域
                container_width = self.original_container.width()
                container_height = self.original_container.height()
                
                # 等比例缩放，修改为0.9确保有边距，避免图像填满容器
                scale = min(container_width/w, container_height/h) * 0.9
                new_w = int(w * scale)
                new_h = int(h * scale)
                
                # 记录显示尺寸和缩放比例，供ROI计算使用
                self.display_size = (new_w, new_h)
                self.current_scale = scale
                
                # 缩放图像
                frame_rgb = cv2.resize(frame_rgb, (new_w, new_h))
                bytes_per_line = ch * new_w
                
                # 创建QImage
                q_img = QImage(frame_rgb.data, new_w, new_h, bytes_per_line, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(q_img)
                
                # 绘制ROI区域
                if self.roi_coords and not self.drawing:
                    self.draw_roi_on_pixmap(pixmap, self.roi_coords, scale)
                
                self.original_label.setPixmap(pixmap)
                
            except Exception as e:
                print(f"显示原始图像时出错: {str(e)}")
                print(f"错误详情: {traceback.format_exc()}")
                # 显示黑屏
                black_image = np.zeros((480, 640, 3), dtype=np.uint8)
                black_qimage = QImage(black_image.data, 640, 480, 640 * 3, QImage.Format_RGB888)
                self.original_label.setPixmap(QPixmap.fromImage(black_qimage))

            # 显示处理后的图像
            try:
                processed_rgb = cv2.cvtColor(processed, cv2.COLOR_BGR2RGB)
                
                # 处理后的图像也使用同样的等比例缩放，保持比例一致
                processed_h, processed_w = processed_rgb.shape[:2]
                proc_scale = min(container_width/processed_w, container_height/processed_h) * 0.9
                proc_new_w = int(processed_w * proc_scale)
                proc_new_h = int(processed_h * proc_scale)
                
                # 缩放处理后的图像
                processed_rgb = cv2.resize(processed_rgb, (proc_new_w, proc_new_h))
                proc_bytes_per_line = ch * proc_new_w
                
                q_processed = QImage(processed_rgb.data, proc_new_w, proc_new_h, proc_bytes_per_line, QImage.Format_RGB888)
                processed_pixmap = QPixmap.fromImage(q_processed)
                
                # 在处理后的图像上不再重复显示ROI框
                self.processed_label.setPixmap(processed_pixmap)
            except Exception as e:
                print(f"显示处理后图像时出错: {str(e)}")
                print(f"错误详情: {traceback.format_exc()}")
                # 显示黑屏
                black_image = np.zeros((480, 640, 3), dtype=np.uint8)
                black_qimage = QImage(black_image.data, 640, 480, 640 * 3, QImage.Format_RGB888)
                self.processed_label.setPixmap(QPixmap.fromImage(black_qimage))
                
        except Exception as e:
            print(f"更新图像时出错: {str(e)}")
            print(f"错误详情: {traceback.format_exc()}")
            # 显示黑屏
            try:
                black_image = np.zeros((480, 640, 3), dtype=np.uint8)
                black_qimage = QImage(black_image.data, 640, 480, 640 * 3, QImage.Format_RGB888)
                self.original_label.setPixmap(QPixmap.fromImage(black_qimage))
                self.processed_label.setPixmap(QPixmap.fromImage(black_qimage))
            except:
                pass
                
    def draw_roi_on_pixmap(self, pixmap, roi_coords, scale):
        """在pixmap上绘制ROI区域"""
        try:
            painter = QPainter(pixmap)
            painter.setPen(QPen(QColor(255, 0, 0), 2))  # 使用红色线条显示已确定的ROI
            
            # 缩放ROI坐标到显示尺寸
            # 使用ROI的真实坐标，应用显示比例进行缩放
            x1, y1, x2, y2 = roi_coords
            
            # 确保x1,y1是左上角，x2,y2是右下角
            left = int(min(x1, x2) * scale)
            top = int(min(y1, y2) * scale)
            width = int(abs(x2 - x1) * scale)
            height = int(abs(y2 - y1) * scale)
            
            # 绘制矩形
            painter.drawRect(left, top, width, height)
            painter.end()
        except Exception as e:
            print(f"绘制ROI区域时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def save_thresholds(self):
        if self.mode == "HSV":
            lower = np.array([
                self.h_min_slider.value(),
                self.s_min_slider.value(),
                self.v_min_slider.value()
            ])
            upper = np.array([
                self.h_max_slider.value(),
                self.s_max_slider.value(),
                self.v_max_slider.value()
            ])
            save_thresholds("HSV", lower, upper, self.roi_coords if self.roi_coords else default_roi, self.exposure_slider.value())
            QMessageBox.information(self, "保存", "HSV阈值已保存")
        elif self.mode == "红色":
            # 保存红色模式HSV阈值
            with open("thresholds_red.txt", "w") as f:
                # H1通道
                f.write(f"{self.red_h_min1_slider.value()}\n")
                f.write(f"{self.red_h_max1_slider.value()}\n")
                # H2通道
                f.write(f"{self.red_h_min2_slider.value()}\n")
                f.write(f"{self.red_h_max2_slider.value()}\n")
                # S通道
                f.write(f"{self.red_s_min_slider.value()}\n")
                f.write(f"{self.red_s_max_slider.value()}\n")
                # V通道
                f.write(f"{self.red_v_min_slider.value()}\n")
                f.write(f"{self.red_v_max_slider.value()}\n")
                # ROI和曝光度
                f.write(",".join(map(str, self.roi_coords if self.roi_coords else default_roi)) + "\n")
                f.write(f"{self.exposure_slider.value()}\n")
            QMessageBox.information(self, "保存", "红色模式HSV阈值已保存")
        elif self.mode == "灰度":
            lower = self.gray_min_slider.value()
            upper = self.gray_max_slider.value()
            save_thresholds("灰度", lower, upper, self.roi_coords if self.roi_coords else default_roi, self.exposure_slider.value())
            QMessageBox.information(self, "保存", "灰度阈值已保存")
        elif self.mode == "RGB":
            lower = np.array([
                self.r_min_slider.value(),
                self.g_min_slider.value(),
                self.b_min_slider.value()
            ])
            upper = np.array([
                self.r_max_slider.value(),
                self.g_max_slider.value(),
                self.b_max_slider.value()
            ])
            save_thresholds("RGB", lower, upper, self.roi_coords if self.roi_coords else default_roi, self.exposure_slider.value())
            QMessageBox.information(self, "保存", "RGB阈值已保存")

    def set_default_thresholds(self):
        if self.mode == "HSV":
            lower, upper, roi, exposure = read_thresholds(self.mode)
            if lower is None or upper is None or roi is None:
                # 使用默认值
                lower = np.array([0, 0, 0])
                upper = np.array([179, 255, 255])
                roi = default_roi
                exposure = 0
            self.roi_coords = roi
            self.h_min_slider.setValue(lower[0])
            self.s_min_slider.setValue(lower[1])
            self.v_min_slider.setValue(lower[2])
            self.h_max_slider.setValue(upper[0])
            self.s_max_slider.setValue(upper[1])
            self.v_max_slider.setValue(upper[2])
            self.exposure_slider.setValue(exposure)
        elif self.mode == "红色":
            try:
                with open("thresholds_red.txt", "r") as f:
                    lines = f.readlines()
                    # H1通道
                    self.red_h_min1_slider.setValue(int(lines[0].strip()))
                    self.red_h_max1_slider.setValue(int(lines[1].strip()))
                    # H2通道
                    self.red_h_min2_slider.setValue(int(lines[2].strip()))
                    self.red_h_max2_slider.setValue(int(lines[3].strip()))
                    # S通道
                    self.red_s_min_slider.setValue(int(lines[4].strip()))
                    self.red_s_max_slider.setValue(int(lines[5].strip()))
                    # V通道
                    self.red_v_min_slider.setValue(int(lines[6].strip()))
                    self.red_v_max_slider.setValue(int(lines[7].strip()))
                    # ROI和曝光度
                    self.roi_coords = [int(x) for x in lines[8].strip().split(",")]
                    self.exposure_slider.setValue(int(lines[9].strip()))
            except FileNotFoundError:
                # 使用默认HSV值
                self.red_h_min1_slider.setValue(0)
                self.red_h_max1_slider.setValue(179)  # 修改为最大值
                self.red_h_min2_slider.setValue(0)
                self.red_h_max2_slider.setValue(179)  # 修改为最大值
                self.red_s_min_slider.setValue(0)
                self.red_s_max_slider.setValue(255)
                self.red_v_min_slider.setValue(0)
                self.red_v_max_slider.setValue(255)
        elif self.mode == "灰度":
            lower, upper, roi, exposure = read_thresholds(self.mode)
            if lower is None or upper is None or roi is None:
                # 使用默认值
                lower = 0
                upper = 255
                roi = default_roi
                exposure = 0
            self.roi_coords = roi
            self.gray_min_slider.setValue(lower)
            self.gray_max_slider.setValue(upper)
            self.exposure_slider.setValue(exposure)
        elif self.mode == "RGB":
            lower, upper, roi, exposure = read_thresholds(self.mode)
            if lower is None or upper is None or roi is None:
                # 使用默认值
                lower = np.array([0, 0, 0])
                upper = np.array([255, 255, 255])
                roi = default_roi
                exposure = 0
            self.roi_coords = roi
            self.r_min_slider.setValue(lower[0])
            self.g_min_slider.setValue(lower[1])
            self.b_min_slider.setValue(lower[2])
            self.r_max_slider.setValue(upper[0])
            self.g_max_slider.setValue(upper[1])
            self.b_max_slider.setValue(upper[2])
            self.exposure_slider.setValue(exposure)

    def reset_hsv_thresholds(self):
        """重置HSV阈值"""
        self.h_min_slider.setValue(0)
        self.h_max_slider.setValue(179)
        self.s_min_slider.setValue(0)
        self.s_max_slider.setValue(255)
        self.v_min_slider.setValue(0)
        self.v_max_slider.setValue(255)
        self.update_frame()

    def reset_gray_thresholds(self):
        self.gray_min_slider.setValue(0)
        self.gray_max_slider.setValue(255)
        self.update_frame()

    def reset_rgb_thresholds(self):
        self.r_min_slider.setValue(0)
        self.g_min_slider.setValue(0)
        self.b_min_slider.setValue(0)
        self.r_max_slider.setValue(255)
        self.g_max_slider.setValue(255)
        self.b_max_slider.setValue(255)
        self.update_frame()

    def reset_red_thresholds(self):
        """重置红色模式阈值"""
        self.red_h_min1_slider.setValue(0)
        self.red_h_max1_slider.setValue(179)  # 修改为最大值
        self.red_h_min2_slider.setValue(0)
        self.red_h_max2_slider.setValue(179)  # 修改为最大值
        self.red_s_min_slider.setValue(0)
        self.red_s_max_slider.setValue(255)
        self.red_v_min_slider.setValue(0)
        self.red_v_max_slider.setValue(255)
        self.update_frame()

    def toggle_invert(self):
        self.is_inverted = not self.is_inverted
        self.update_frame()

    def eventFilter(self, source, event):
        # 只处理原始图像标签的鼠标事件
        if source is self.original_label:
            # 鼠标按下事件
            if event.type() == event.MouseButtonPress and event.button() == Qt.LeftButton:
                if not self.camera_opened or self.current_frame is None:
                    return True
                    
                self.drawing = True
                self.start_point = event.pos()
                self.end_point = event.pos()
                print(f"鼠标按下位置: ({self.start_point.x()}, {self.start_point.y()})")
                
                # 重新获取原始图像的副本，以便后续绘制
                self.cached_pixmap = self.original_label.pixmap().copy()
                
                return True
                
            # 鼠标移动事件
            elif event.type() == event.MouseMove and self.drawing:
                self.end_point = event.pos()
                # 使用临时绘制，不调用update_frame
                self.draw_temp_roi()
                return True
                
            # 鼠标释放事件
            elif event.type() == event.MouseButtonRelease and event.button() == Qt.LeftButton and self.drawing:
                self.drawing = False
                self.end_point = event.pos()
                print(f"鼠标释放位置: ({self.end_point.x()}, {self.end_point.y()})")
                
                # 计算并设置ROI
                self.calculate_roi()
                # 清除缓存的pixmap
                self.cached_pixmap = None
                return True
                
        # 对于其他事件，让默认事件处理器处理
        return super().eventFilter(source, event)

    def draw_temp_roi(self):
        """临时绘制ROI区域"""
        if not self.drawing or not self.original_label.pixmap() or not self.start_point or not self.end_point:
            return
            
        # 使用缓存的pixmap作为基础进行绘制，避免重复绘制
        if not hasattr(self, 'cached_pixmap') or self.cached_pixmap is None:
            self.cached_pixmap = self.original_label.pixmap().copy()
        
        # 创建pixmap副本用于绘制
        pixmap_copy = self.cached_pixmap.copy()
        
        # 绘制临时ROI
        painter = QPainter(pixmap_copy)
        painter.setPen(QPen(QColor(0, 255, 0), 2))  # 绿色
        
        # 原始标签尺寸
        label_size = self.original_label.size()
        pixmap_size = pixmap_copy.size()
        
        # 计算图像在标签中的偏移（居中显示）
        offset_x = (label_size.width() - pixmap_size.width()) // 2
        offset_y = (label_size.height() - pixmap_size.height()) // 2
        
        # 转换鼠标坐标到图像坐标
        x1 = self.start_point.x() - offset_x
        y1 = self.start_point.y() - offset_y
        x2 = self.end_point.x() - offset_x
        y2 = self.end_point.y() - offset_y
        
        # 确保坐标在显示图像范围内
        x1 = max(0, min(x1, pixmap_size.width() - 1))
        y1 = max(0, min(y1, pixmap_size.height() - 1))
        x2 = max(0, min(x2, pixmap_size.width() - 1))
        y2 = max(0, min(y2, pixmap_size.height() - 1))
        
        # 绘制矩形（使用min和max确保正确绘制矩形，无论拖动方向）
        left = min(x1, x2)
        top = min(y1, y2)
        width = abs(x2 - x1)
        height = abs(y2 - y1)
        
        painter.drawRect(left, top, width, height)
        painter.end()
        
        # 更新显示
        self.original_label.setPixmap(pixmap_copy)

    def calculate_roi(self):
        """计算并设置ROI坐标"""
        try:
            if not self.camera_opened or self.current_frame is None:
                return
                
            # 获取当前图像尺寸信息
            frame_height, frame_width = self.current_frame.shape[:2]
            print(f"原始帧尺寸: {frame_width}x{frame_height}")
            
            # 获取显示相关信息
            label_size = self.original_label.size()
            pixmap = self.original_label.pixmap()
            if not pixmap:
                return
                
            pixmap_size = pixmap.size()
            print(f"标签尺寸: {label_size.width()}x{label_size.height()}")
            print(f"显示图像尺寸: {pixmap_size.width()}x{pixmap_size.height()}")
            
            # 计算图像在标签中的偏移（居中显示）
            offset_x = (label_size.width() - pixmap_size.width()) // 2
            offset_y = (label_size.height() - pixmap_size.height()) // 2
            print(f"图像在标签中的偏移: ({offset_x}, {offset_y})")
            
            # 转换鼠标坐标到图像坐标
            x1 = self.start_point.x() - offset_x
            y1 = self.start_point.y() - offset_y
            x2 = self.end_point.x() - offset_x
            y2 = self.end_point.y() - offset_y
            
            # 确保坐标在显示图像范围内
            x1 = max(0, min(x1, pixmap_size.width() - 1))
            y1 = max(0, min(y1, pixmap_size.height() - 1))
            x2 = max(0, min(x2, pixmap_size.width() - 1))
            y2 = max(0, min(y2, pixmap_size.height() - 1))
            
            # 使用已保存的缩放比例进行坐标转换
            if hasattr(self, 'current_scale') and self.current_scale > 0:
                scale_factor = 1.0 / self.current_scale
                print(f"使用已缓存的缩放比例: {self.current_scale}, 缩放因子: {scale_factor}")
            else:
                # 如果没有保存缩放比例，则计算比例
                scale_factor = frame_width / pixmap_size.width()
                print(f"计算的缩放因子: {scale_factor}")
            
            # 计算原始图像上的ROI坐标 - 使用精确的浮点数计算，避免精度损失
            roi_x1 = int(min(x1, x2) * scale_factor)
            roi_y1 = int(min(y1, y2) * scale_factor)
            roi_x2 = int(max(x1, x2) * scale_factor)
            roi_y2 = int(max(y1, y2) * scale_factor)
            
            # 确保坐标在原始图像范围内
            roi_x1 = max(0, min(roi_x1, frame_width - 1))
            roi_y1 = max(0, min(roi_y1, frame_height - 1))
            roi_x2 = max(0, min(roi_x2, frame_width - 1))
            roi_y2 = max(0, min(roi_y2, frame_height - 1))
            
            print(f"显示图像上的选择区域: ({min(x1, x2)}, {min(y1, y2)}) - ({max(x1, x2)}, {max(y1, y2)})")
            print(f"原始帧上的选择区域: ({roi_x1}, {roi_y1}) - ({roi_x2}, {roi_y2})")
            
            # 检查ROI有效性
            if roi_x2 - roi_x1 > 10 and roi_y2 - roi_y1 > 10:
                self.roi_coords = [roi_x1, roi_y1, roi_x2, roi_y2]
                print(f"设置ROI区域: {self.roi_coords}")
            else:
                print("ROI区域太小，忽略选择")
                self.roi_coords = None
                
            # 更新图像显示
            self.update_frame()
        except Exception as e:
            print(f"计算ROI时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            self.roi_coords = None

    def update_exposure(self):
        """更新曝光度"""
        try:
            if self.capture is not None and self.camera_opened:
                exposure_value = self.exposure_slider.value()
                # 先关闭自动曝光
                self.capture.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0)
                # 设置曝光度
                self.capture.set(cv2.CAP_PROP_EXPOSURE, exposure_value)
                print(f"更新曝光度为: {exposure_value}")
        except Exception as e:
            print(f"设置曝光度时出错: {str(e)}")
            QMessageBox.warning(self, "错误", f"设置曝光度失败: {str(e)}")

    def check_camera_status(self):
        """定期检查摄像头状态"""
        if self.camera_opened and self.capture is not None:
            try:
                # 检查摄像头是否仍然可用
                if not self.capture.isOpened():
                    print("摄像头状态检查: 摄像头已断开连接")
                    self.close_camera()
                    QMessageBox.warning(self, "警告", "摄像头连接已断开，已自动关闭")
                    return
                    
                # 尝试读取一帧，但不显示
                ret, _ = self.capture.read()
                if not ret:
                    self.fail_count += 1
                    print(f"摄像头状态检查: 读取失败 ({self.fail_count}/3)")
                    
                    if self.fail_count >= 3:  # 连续3次检查失败
                        print("摄像头状态检查: 多次读取失败，关闭摄像头")
                        self.close_camera()
                        QMessageBox.warning(self, "警告", "摄像头无响应，已自动关闭")
                        self.fail_count = 0
                else:
                    # 读取成功，重置失败计数
                    self.fail_count = 0
            except Exception as e:
                print(f"摄像头状态检查出错: {str(e)}")
                self.close_camera()
                QMessageBox.warning(self, "警告", "摄像头状态异常，已自动关闭")
        else:
            # 摄像头未打开，重置失败计数
            self.fail_count = 0

    def closeEvent(self, event):
        """关闭窗口时释放资源"""
        try:
            print("正在关闭窗口...")
            
            # 停止所有定时器
            if hasattr(self, 'timer') and self.timer is not None:
                self.timer.stop()
                
            if hasattr(self, 'camera_check_timer') and self.camera_check_timer is not None:
                self.camera_check_timer.stop()
            
            # 关闭摄像头
            if self.camera_opened:
                try:
                    self.close_camera()
                    # 等待摄像头完全关闭
                    import time
                    time.sleep(0.2)
                except Exception as cam_err:
                    print(f"关闭摄像头时出错: {str(cam_err)}")
                    
            # 释放其他资源
            self.current_frame = None
            
            print("资源释放完成，窗口已关闭")
            event.accept()
        except Exception as e:
            print(f"关闭窗口时出错: {str(e)}")
            # 确保窗口能够关闭
            event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ImageProcessor()
    window.show()
    sys.exit(app.exec_())