from maix import image, display, app, time, camera
import cv2
import numpy as np


PIXEL_RADIUS = 3  # 用于计算像素和的半径

# 初始化显示和摄像头
disp = display.Display()
cam = camera.Camera(320, 240, image.Format.FMT_BGR888)

while not app.need_exit():
    # 读取并转换图像
    img = cam.read()
    img = image.image2cv(img, ensure_bgr=True, copy=False)

    # 转 HSV 进行颜色分割
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    # 红色范围
    lower_red1 = np.array([0, 100, 50])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 100, 50])
    upper_red2 = np.array([180, 255, 255])
    # 绿色范围
    lower_green = np.array([40, 100, 50])
    upper_green = np.array([80, 255, 255])

    mask_red = cv2.bitwise_or(
        cv2.inRange(hsv, lower_red1, upper_red1),
        cv2.inRange(hsv, lower_red2, upper_red2)
    )
    mask_green = cv2.inRange(hsv, lower_green, upper_green)

    # 闭运算去噪
    kernel = np.ones((5, 5), np.uint8)
    mask_red = cv2.morphologyEx(mask_red, cv2.MORPH_CLOSE, kernel)
    mask_green = cv2.morphologyEx(mask_green, cv2.MORPH_CLOSE, kernel)

    # 查找红色激光轮廓
    contours_red, _ = cv2.findContours(mask_red, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for cnt in contours_red:
        rect = cv2.minAreaRect(cnt)
        
        cx, cy = map(int, rect[0])
        # 计算 (cx,cy) 小区域的 R/G 和
        h, w = img.shape[:2]
        x0, y0 = max(0, cx - PIXEL_RADIUS), max(0, cy - PIXEL_RADIUS)
        x1, y1 = min(w-1, cx + PIXEL_RADIUS), min(h-1, cy + PIXEL_RADIUS)
        roi = img[y0:y1, x0:x1]
        r_sum = int(roi[:, :, 2].sum())
        g_sum = int(roi[:, :, 1].sum())
        if r_sum > g_sum:
            cv2.circle(img, (cx, cy), 5, (0, 0, 255), -1)
            cv2.putText(img, "Red Laser", (cx - 20, cy - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

    # 查找绿色激光轮廓
    contours_green, _ = cv2.findContours(mask_green, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for cnt in contours_green:
        rect = cv2.minAreaRect(cnt)
        w, h = map(int, rect[1])
        if w<3 and h<3:
            continue
        print("高是",h)
        print("宽是",w)
        cx, cy = map(int, rect[0])
        # 计算 (cx,cy) 小区域的 R/G 和
        h, w = img.shape[:2]
        x0, y0 = max(0, cx - PIXEL_RADIUS), max(0, cy - PIXEL_RADIUS)
        x1, y1 = min(w-1, cx + PIXEL_RADIUS), min(h-1, cy + PIXEL_RADIUS)
        roi = img[y0:y1, x0:x1]
        r_sum = int(roi[:, :, 2].sum())
        g_sum = int(roi[:, :, 1].sum())
        if g_sum > r_sum:
            cv2.circle(img, (cx, cy), 5, (0, 255, 0), -1)
            cv2.putText(img, "Green Laser", (cx - 20, cy - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

    # 显示最终结果
    disp.show(image.cv2image(img, bgr=True, copy=False))
