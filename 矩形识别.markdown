# 多边形识别

![image-20250427143352496](矩形识别.assets/image-20250427143352496.png)

如何识别矩形？---->如何识别多边形？

对于我们的视觉模块k230和openmv而言 有find_rects这样方便的函数可以直接寻找，但是只能进行矩形的寻找，如果需要进行其他多边形的寻找只有非常规方法。而对于opencv来说，我们可以使用库里面函数来组合来达到我们想要的效果！！！

在opencv里面识别多边形常用的手段就是边缘寻找 再进行多边形逼近。

## 1.首先为了提升处理速度 可以忽略掉一些不重要的信息，对于我们的形状来说 色彩信息就是不重要的信息。

那我们就把我们的图像进行灰度二值化。

在 OpenCV 里，`threshold`（阈值处理）属于基本且关键的图像处理操作，主要用于将图像的像素值进行二值化处理，也就是把图像的像素值转化为两种状态（通常是 0 和 255），以此突出图像里的目标物体。下面为你详细介绍 OpenCV 中`threshold`函数的相关内容。

### 函数原型

在 Python 的 OpenCV 里，`threshold`函数的原型如下：

```python
ret, thresh = cv2.threshold(src, thresh, maxval, type)
```

各参数的含义如下：

- `src`：待处理的输入图像，通常是单通道的灰度图像。
- `thresh`：设定的阈值，用于对像素值进行分类。
- `maxval`：当像素值超过阈值时，所赋予的新像素值。
- `type`：阈值处理的类型，常见的类型有：
  - `cv2.THRESH_BINARY`：二值化处理，超过阈值的像素设为`maxval`，其余设为 0。
  - `cv2.THRESH_BINARY_INV`：反向二值化处理，超过阈值的像素设为 0，其余设为`maxval`。
  - `cv2.THRESH_TRUNC`：截断处理，超过阈值的像素设为阈值，其余保持不变。
  - `cv2.THRESH_TOZERO`：超过阈值的像素保持不变，其余设为 0。
  - `cv2.THRESH_TOZERO_INV`：反向的 TOZERO 处理，超过阈值的像素设为 0，其余保持不变。
- `ret`：返回的阈值，通常与传入的`thresh`相同。
- `thresh`：阈值处理后的输出图像。

```
import cv2

# 读取图片
image = cv2.imread('rect\PixPin_2025-04-27_14-39-17.png')

if image is None:
    print("错误: 无法读取图片，请检查图片路径。")
else:
    # 定义缩放比例
    scale_percent = 50
    width = int(image.shape[1] * scale_percent / 100)
    height = int(image.shape[0] * scale_percent / 100)
    dim = (width, height)

    # 缩放图像
    resized_image = cv2.resize(image, dim, interpolation=cv2.INTER_AREA)
    # 转换为灰度图像
    gray_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2GRAY)
    # 进行二值化处理
    _, binary_image = cv2.threshold(gray_image, 240, 255, cv2.THRESH_BINARY_INV)

    # 显示原始图像
    cv2.imshow('Original Image', resized_image)
    # 显示灰度图像
    cv2.imshow('Grayscale Image', gray_image)
    # 显示二值化图像
    cv2.imshow('Binary Image', binary_image)

    # 等待按键关闭窗口
    cv2.waitKey(0)
    # 关闭所有窗口
    cv2.destroyAllWindows()
    
```

![image-20250427154712264](矩形识别.assets/image-20250427154712264.png)

## 2.现在开始寻找边缘

重要函数介绍：

```python
contours, hierarchy = cv2.findContours(image, mode, method[, contours[, hierarchy[, offset]]])
```

以下是对各个参数的详细解释：

- `image`

  - 输入的二值图像，通常是经过边缘检测或者阈值处理后的图像。在前面的代码示例中，使用的是经过二值化处理后的 `binary_image`。

- `mode`

  - 轮廓检索模式，它决定了如何检索轮廓以及轮廓之间的层次关系。常见的取值有：
    - **`cv2.RETR_EXTERNAL`**：只检测最外层的轮廓，忽略所有的内部轮廓。适用于只关心物体外部边界的场景。
    - **`cv2.RETR_LIST`**：检索所有的轮廓，但不建立轮廓之间的层次关系，所有轮廓被视为独立的个体。
    - **`cv2.RETR_TREE`**：检索所有的轮廓，并建立完整的轮廓层次结构，每个轮廓都有明确的父轮廓和子轮廓信息。
    - **`cv2.RETR_CCOMP`**：检索所有的轮廓，并将它们组织成两级层次结构，外层轮廓属于顶层，内层轮廓属于底层。

- `method`

  - 轮廓逼近方法，它决定了如何表示检测到的轮廓。常见的取值有：
    - **`cv2.CHAIN_APPROX_NONE`**：存储所有的轮廓点，即轮廓上的每一个点都会被保留。
    - **`cv2.CHAIN_APPROX_SIMPLE`**：只保留轮廓的端点，对于水平、垂直和对角线方向的直线段，只保留其起点和终点，这样可以大大减少存储轮廓所需的点数。

- `offset`

  （可选）：

  - 可选参数，类型为 `(int, int)`，表示轮廓点的偏移量。默认值为 `(0, 0)`，即不进行偏移。

### 返回值

`cv2.findContours` 函数返回两个值：

- `contours`

  - 一个列表，列表中的每个元素都是一个表示轮廓的 `numpy` 数组。每个数组包含了构成该轮廓的一系列点的坐标。在前面的代码示例中，`contours` 列表存储了在二值图像中检测到的所有轮廓。

- `hierarchy`

  - 一个 `numpy` 数组，用于表示轮廓之间的层次关系。该数组的形状为 `(1, n, 4)`，其中 `n` 是检测到的轮廓数量。对于每个轮廓，数组中的四个值分别表示该轮廓的下一个轮廓、前一个轮廓、子轮廓和父轮廓的索引。如果某个轮廓没有对应的下一个、前一个、子轮廓或父轮廓，则对应的索引值为 -1。

    ```
    import cv2
    
    # 读取图片
    image = cv2.imread('rect\PixPin_2025-04-27_14-39-17.png')
    
    if image is None:
        print("错误: 无法读取图片，请检查图片路径。")
    else:
        # 定义缩放比例
        scale_percent = 50
        width = int(image.shape[1] * scale_percent / 100)
        height = int(image.shape[0] * scale_percent / 100)
        dim = (width, height)
    
        # 缩放图像
        resized_image = cv2.resize(image, dim, interpolation=cv2.INTER_AREA)
        # 转换为灰度图像
        gray_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2GRAY)
        # 进行二值化处理
        _, binary_image = cv2.threshold(gray_image, 240, 255, cv2.THRESH_BINARY_INV)
    
        # 查找轮廓
        contours, _ = cv2.findContours(binary_image, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
    
        # 在原始缩放图像上绘制轮廓
        contour_image = resized_image.copy()
        cv2.drawContours(contour_image, contours, -1, (0, 255, 0), 2)
    
    
        cv2.imshow('Contour Image', contour_image)
        # 显示原始图像
        cv2.imshow('Original Image', resized_image)
        # 显示灰度图像
        cv2.imshow('Grayscale Image', gray_image)
        # 显示二值化图像
        cv2.imshow('Binary Image', binary_image)
    
        # 等待按键关闭窗口
        cv2.waitKey(0)
        # 关闭所有窗口
        cv2.destroyAllWindows()
        
    ```

    <img src="矩形识别.assets/image-20250427154748347.png" alt="image-20250427154748347" style="zoom:50%;" />

## 3.现在多边形逼近

`cv2.approxPolyDP` 是 OpenCV 里用于对轮廓进行多边形逼近的函数。该函数能将一个具有很多点的轮廓用较少的点构成的多边形来近似表示，在保留轮廓基本形状的同时减少存储和处理的数据量。下面详细介绍其参数和功能。

### 函数原型

```python
approximated_contour = cv2.approxPolyDP(curve, epsilon, closed)
```

### 参数解释

#### 1. `curve`

- **类型**：`numpy.ndarray`
- **功能**：输入的轮廓，是一个 `numpy` 数组，包含了轮廓上所有点的坐标。在之前的代码中，这个参数通常是从 `cv2.findContours` 函数返回的 `contours` 列表里取出的单个轮廓。例如：

```python
contours, _ = cv2.findContours(binary_image, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
for contour in contours:
    # 这里的 contour 就是传入 cv2.approxPolyDP 的 curve 参数
    approximated = cv2.approxPolyDP(contour, epsilon, True)
```

#### 2. `epsilon`

- **类型**：`float`
- **功能**：该参数是一个精度参数，用于控制逼近的程度。它表示原始轮廓与逼近后的多边形之间的最大距离。具体来说，`epsilon` 是一个相对值，通常是根据轮廓的周长来计算的。比如在之前的代码里：

```python
epsilon = 0.005 * cv2.arcLength(contour, True)
approx = cv2.approxPolyDP(contour, epsilon, True)
```

这里 `cv2.arcLength(contour, True)` 计算的是轮廓的周长，`epsilon` 被设置为周长的 `0.005` 倍。`epsilon` 值越小，逼近后的多边形就越接近原始轮廓，所需的点数也就越多；反之，`epsilon` 值越大，逼近后的多边形就越简单，点数越少。

#### 3. `closed`

- **类型**：`bool`
- **功能**：该参数用于指定逼近后的多边形是否为封闭的。若 `closed` 为 `True`，则表示逼近后的多边形是封闭的，即最后一个点会与第一个点相连；若为 `False`，则表示多边形是开放的。在实际应用中，对于轮廓的逼近通常将其设置为 `True`，因为轮廓一般代表的是封闭的物体边界。

### 返回值

- **类型**：`numpy.ndarray`
- **功能**：返回一个 `numpy` 数组，包含了逼近后的多边形的顶点坐标。这些顶点按顺序连接起来就构成了逼近后的多边形。

```
import cv2

# 读取图片
image = cv2.imread('rect\PixPin_2025-04-27_14-39-17.png')

if image is None:
    print("错误: 无法读取图片，请检查图片路径。")
else:
    # 定义缩放比例
    scale_percent = 50
    width = int(image.shape[1] * scale_percent / 100)
    height = int(image.shape[0] * scale_percent / 100)
    dim = (width, height)

    # 缩放图像
    resized_image = cv2.resize(image, dim, interpolation=cv2.INTER_AREA)
    # 转换为灰度图像
    gray_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2GRAY)
    # 进行二值化处理
    _, binary_image = cv2.threshold(gray_image, 240, 255, cv2.THRESH_BINARY_INV)

    # 查找轮廓
    contours, _ = cv2.findContours(binary_image, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)

    # 指定要显示的边数
    target_sides = 4
    approximated_contours = []
    for contour in contours:
        # 进行多边形逼近
        epsilon = 0.03 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        if len(approx) == target_sides:
            approximated_contours.append(approx)

    # 在原始缩放图像上绘制指定边数的轮廓
    contour_image = resized_image.copy()
    cv2.drawContours(contour_image, approximated_contours, -1, (0, 255, 0), 2)

    # 计算并绘制每个指定轮廓的矩（质心）
    for contour in approximated_contours:
        M = cv2.moments(contour)
        if M["m00"] != 0:
            cX = int(M["m10"] / M["m00"])
            cY = int(M["m01"] / M["m00"])
            # 在质心位置绘制一个小圆
            cv2.circle(contour_image, (cX, cY), 5, (0, 0, 255), -1)
            # 打印质心坐标
            print(f"质心坐标: ({cX}, {cY})")

    # 显示原始图像
    cv2.imshow('Original Image', resized_image)
    # 显示灰度图像
    cv2.imshow('Grayscale Image', gray_image)
    # 显示二值化图像
    cv2.imshow('Binary Image', binary_image)

    # 显示带有指定边数轮廓和质心的图像
    cv2.imshow('Contour Image with Specified Sides and Centroids', contour_image)

    # 等待按键关闭窗口
    cv2.waitKey(0)
    # 关闭所有窗口
    cv2.destroyAllWindows()
    
```

![image-20250427155419883](矩形识别.assets/image-20250427155419883.png)

## 结合摄像头开始寻找矩形

```
import cv2

# 打开摄像头
cap = cv2.VideoCapture(0)

if not cap.isOpened():
    print("无法打开摄像头，请检查设备连接。")
else:
    # 定义最小轮廓面积
    min_contour_area = 1000
    max_contour_area = 30000

    while True:
        # 读取一帧图像
        ret, frame = cap.read()

        if not ret:
            print("无法读取帧，请检查摄像头。")
            break

        # 定义缩放比例
        scale_percent = 50
        width = int(frame.shape[1] * scale_percent / 100)
        height = int(frame.shape[0] * scale_percent / 100)
        dim = (width, height)

        # 缩放图像
        resized_image = cv2.resize(frame, dim, interpolation=cv2.INTER_AREA)
        # 转换为灰度图像
        gray_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2GRAY)
        # 进行二值化处理
        _, binary_image = cv2.threshold(gray_image, 46, 255, cv2.THRESH_BINARY)

        # 查找轮廓
        contours, _ = cv2.findContours(binary_image, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        # 指定要显示的边数
        target_sides = 4
        approximated_contours = []
        for contour in contours:
            # 计算轮廓面积
            area = cv2.contourArea(contour)
            if area > min_contour_area and area < max_contour_area:
                # 计算轮廓周长
                print("轮廓面积:", area)
                # 进行多边形逼近
                epsilon = 0.03 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                if len(approx) == target_sides:
                    approximated_contours.append((approx, area))

        # 按面积排序，面积大的为外框，面积小的为内框
        approximated_contours.sort(key=lambda x: x[1], reverse=True)

        # 在原始缩放图像上绘制指定边数的轮廓
        contour_image = resized_image.copy()
        for i, (approx, area) in enumerate(approximated_contours):
            if i == 0:  # 最大面积的为外框，用红色绘制
                color = (0, 0, 255)
            else:  # 其他为内框，用绿色绘制
                color = (0, 255, 0)
            cv2.drawContours(contour_image, [approx], -1, color, 2)

        # 显示原始图像
        cv2.imshow('Original Image', resized_image)
        # 显示灰度图像
        cv2.imshow('Grayscale Image', gray_image)
        # 显示二值化图像
        cv2.imshow('Binary Image', binary_image)
        # 显示带有指定边数轮廓的图像
        cv2.imshow('Contour Image with Specified Sides', contour_image)

        # 按 'Esc' 键退出循环
        if cv2.waitKey(1) == 27:
            break

    # 释放摄像头并关闭所有窗口
    cap.release()
    cv2.destroyAllWindows()
    
```

<img src="矩形识别.assets/image-20250427154818334.png" alt="image-20250427154818334" style="zoom:50%;" />

# 激光识别

```
import cv2
import numpy as np
import os

# 全局参数
GRAY_MIN = 226
GRAY_MAX = 255
EXPOSURE = -7
ROI = [95,115,568,464]  # x1, y1, x2, y2
INVERT = False

# 膨胀参数
DILATE_KERNEL_SIZE = 4
DILATE_ITERATIONS = 4

# 打开摄像头
cap = cv2.VideoCapture(0)

# 获取摄像头尺寸
cam_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
cam_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

# 计算窗口尺寸，保持视频原始比例
# 假设设计窗口宽度480，高度根据比例计算
display_width = 480
display_height = int(display_width * cam_height / cam_width)


# 创建自定义大小的窗口
cv2.namedWindow("Original", cv2.WINDOW_NORMAL)
cv2.namedWindow("Binary", cv2.WINDOW_NORMAL)
cv2.namedWindow("Edges", cv2.WINDOW_NORMAL)
cv2.namedWindow("Contours", cv2.WINDOW_NORMAL)
cv2.namedWindow("Rectangles", cv2.WINDOW_NORMAL)

# 调整窗口位置和大小
cv2.moveWindow("Original", 50, 50)
cv2.moveWindow("Binary", 50, 50 + display_height + 40)
cv2.moveWindow("Edges", 50 + display_width + 20, 50)
cv2.moveWindow("Contours", 50 + display_width + 20, 50 + display_height + 40)
cv2.moveWindow("Rectangles", 50 + 2 * (display_width + 20), 50)

# 调整窗口大小
cv2.resizeWindow("Original", display_width, display_height)
cv2.resizeWindow("Binary", display_width, display_height)
cv2.resizeWindow("Edges", display_width, display_height)
cv2.resizeWindow("Contours", display_width, display_height)
cv2.resizeWindow("Rectangles", display_width, display_height)

# 构建参数文本，包含控制参数信息
mode_text = "使用默认参数"
dilate_text = f"膨胀: 核大小={DILATE_KERNEL_SIZE}, 迭代={DILATE_ITERATIONS}"
param_text = f"Gray: ({GRAY_MIN}-{GRAY_MAX}) Exp:{EXPOSURE} Mode:{mode_text} {dilate_text}"
print(param_text)

while True:
    # 读取帧
    ret, frame = cap.read()
    if not ret:
        print("无法从摄像头读取")
        break

    # 检查空帧
    if frame is None or frame.size == 0:
        continue

    # 复制帧以避免修改原始数据
    frame = frame.copy()
    orig_frame = frame.copy()
    roi_frame = None

    # 最终处理结果
    binary_result = None        # 二值化结果
    contour_result = None       # 轮廓结果
    edge_result = None          # 边缘检测结果
    rectangle_result = None     # 矩形框结果

    # 应用ROI
    if ROI is not None:
        x1, y1, x2, y2 = ROI
        height, width = frame.shape[:2]

        # 确保坐标在图像范围内
        x1 = max(0, min(x1, width - 1))
        y1 = max(0, min(y1, height - 1))
        x2 = max(0, min(x2, width - 1))
        y2 = max(0, min(y2, height - 1))

        # 检查ROI是否有效
        if x2 > x1 and y2 > y1:
            # 提取ROI，不进行缩放操作
            roi_frame = orig_frame[y1:y2, x1:x2].copy()

            # 在原始图像上绘制ROI区域边框
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # 对ROI区域进行处理
            if roi_frame.size > 0:
                # 转换为灰度图
                gray_roi = cv2.cvtColor(roi_frame, cv2.COLOR_BGR2GRAY)

                # 应用灰度阈值处理
                binary = cv2.inRange(gray_roi, GRAY_MIN, GRAY_MAX)

                # 如果启用反转，则应用反转
                if INVERT:
                    binary = cv2.bitwise_not(binary)

                # 对二值化结果进行膨胀 - 始终应用膨胀
                kernel = np.ones((DILATE_KERNEL_SIZE, DILATE_KERNEL_SIZE), np.uint8)
                binary = cv2.dilate(binary, kernel, iterations=DILATE_ITERATIONS)

                # 创建二值化结果的彩色版本
                binary_result = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)

                # 寻找边缘
                edges = cv2.Canny(binary, 50, 150)
                edge_result = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)

                # 寻找轮廓
                contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                # 创建轮廓显示图像
                contour_result = roi_frame.copy()
                cv2.drawContours(contour_result, contours, -1, (0, 255, 255), 2)  # 黄色线条

                # 创建矩形框显示图像
                rectangle_result = roi_frame.copy()

                # 对每个轮廓画出外接矩形
                for contour in contours:
                    # 仅处理有一定面积的轮廓
                    if cv2.contourArea(contour) > 1:
                        # 获取外接矩形
                        x, y, w, h = cv2.boundingRect(contour)

                        # 分析激光颜色 - 只统计轮廓内部的像素
                        # 创建轮廓掩码
                        mask = np.zeros(roi_frame.shape[:2], dtype=np.uint8)
                        cv2.drawContours(mask, [contour], 0, 255, -1)  # 填充轮廓内部为白色(255)

                        # 使用掩码提取轮廓内的像素
                        masked_image = cv2.bitwise_and(roi_frame, roi_frame, mask=mask)

                        # 计算轮廓内的非零像素数量
                        non_zero_pixels = cv2.countNonZero(mask)
                        if non_zero_pixels == 0:
                            laser_type = "UNKNOWN"
                        else:
                            # 获取各通道的总和，但只统计轮廓内部的像素
                            b_sum = int(np.sum(masked_image[:, :, 0]))
                            g_sum = int(np.sum(masked_image[:, :, 1]))
                            r_sum = int(np.sum(masked_image[:, :, 2]))

                            # 记录调试信息
                            print(f"轮廓内部像素统计 - 红色总和: {r_sum}, 绿色总和: {g_sum}, 蓝色总和: {b_sum}")

                            # 直接比较红绿通道的总和来判断激光类型
                            if g_sum > r_sum - 3000:  # 绿色值显著大于红色
                                laser_type = "GREEN"
                            elif r_sum > g_sum * 1.0:  # 红色值显著大于绿色
                                laser_type = "RED"
                            else:
                                laser_type = "UNKNOWN"

                        # 根据激光类型设置矩形颜色
                        rect_color = (0, 255, 255)  # 默认黄色
                        if laser_type == "RED":
                            rect_color = (0, 0, 255)  # 红色激光用红色矩形
                        elif laser_type == "GREEN":
                            rect_color = (0, 255, 0)  # 绿色激光用绿色矩形

                        # 绘制矩形
                        cv2.rectangle(rectangle_result, (x, y), (x + w, y + h), rect_color, 2)

                        # 计算中心点
                        center_x = x + w // 2
                        center_y = y + h // 2
                        # 在矩形中心画一个十字
                        cv2.drawMarker(rectangle_result, (center_x, center_y), (0, 0, 255),
                                       cv2.MARKER_CROSS, 10, 2)

                        # 显示激光类型文本
                        cv2.putText(rectangle_result, laser_type, (x, y - 5),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, rect_color, 1)
    else:
        # 无有效ROI时，直接处理整个帧
        # 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # 应用灰度阈值处理
        binary = cv2.inRange(gray, GRAY_MIN, GRAY_MAX)

        # 如果启用反转，则应用反转
        if INVERT:
            binary = cv2.bitwise_not(binary)

        # 对二值化结果进行膨胀 - 始终应用膨胀
        kernel = np.ones((DILATE_KERNEL_SIZE, DILATE_KERNEL_SIZE), np.uint8)
        binary = cv2.dilate(binary, kernel, iterations=DILATE_ITERATIONS)

        # 创建二值化结果的彩色版本
        binary_result = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)

        # 寻找边缘
        edges = cv2.Canny(binary, 50, 150)
        edge_result = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)

        # 寻找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 创建轮廓显示图像
        contour_result = frame.copy()
        cv2.drawContours(contour_result, contours, -1, (0, 255, 255), 2)  # 黄色线条

        # 创建矩形框显示图像
        rectangle_result = frame.copy()

        # 对每个轮廓画出外接矩形
        for contour in contours:
            # 仅处理有一定面积的轮廓
            if cv2.contourArea(contour) > 1:
                # 获取外接矩形
                x, y, w, h = cv2.boundingRect(contour)

                # 分析激光颜色 - 只统计轮廓内部的像素
                # 创建轮廓掩码
                mask = np.zeros(frame.shape[:2], dtype=np.uint8)
                cv2.drawContours(mask, [contour], 0, 255, -1)  # 填充轮廓内部为白色(255)

                # 使用掩码提取轮廓内的像素
                masked_image = cv2.bitwise_and(frame, frame, mask=mask)

                # 计算轮廓内的非零像素数量
                non_zero_pixels = cv2.countNonZero(mask)
                if non_zero_pixels == 0:
                    laser_type = "UNKNOWN"
                else:
                    # 获取各通道的总和，但只统计轮廓内部的像素
                    b_sum = int(np.sum(masked_image[:, :, 0]))
                    g_sum = int(np.sum(masked_image[:, :, 1]))
                    r_sum = int(np.sum(masked_image[:, :, 2]))

                    # 记录调试信息
                    print(f"轮廓内部像素统计 - 红色总和: {r_sum}, 绿色总和: {g_sum}, 蓝色总和: {b_sum}")

                    # 直接比较红绿通道的总和来判断激光类型
                    if g_sum > r_sum - 3000:  # 绿色值显著大于红色
                        laser_type = "GREEN"
                    elif r_sum > g_sum * 1.0:  # 红色值显著大于绿色
                        laser_type = "RED"
                    else:
                        laser_type = "UNKNOWN"

                # 根据激光类型设置矩形颜色
                rect_color = (0, 255, 255)  # 默认黄色
                if laser_type == "RED":
                    rect_color = (0, 0, 255)  # 红色激光用红色矩形
                elif laser_type == "GREEN":
                    rect_color = (0, 255, 0)  # 绿色激光用绿色矩形

                # 绘制矩形
                cv2.rectangle(rectangle_result, (x, y), (x + w, y + h), rect_color, 2)

                # 计算中心点
                center_x = x + w // 2
                center_y = y + h // 2
                # 在矩形中心画一个十字
                cv2.drawMarker(rectangle_result, (center_x, center_y), (0, 0, 255),
                               cv2.MARKER_CROSS, 10, 2)

                # 显示激光类型文本
                cv2.putText(rectangle_result, laser_type, (x, y - 5),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, rect_color, 1)

    # 添加参数文本到原始图像
    cv2.putText(frame, param_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

    # 显示所有处理阶段的图像
    cv2.imshow("Original", frame)

    if binary_result is not None:
        cv2.imshow("Binary", binary_result)

    if edge_result is not None:
        cv2.imshow("Edges", edge_result)

    if contour_result is not None:
        cv2.imshow("Contours", contour_result)

    if rectangle_result is not None:
        cv2.imshow("Rectangles", rectangle_result)

    # 等待按键
    key = cv2.waitKey(30)
    if key == 27:  # ESC键退出
        break

# 释放资源
cap.release()
cv2.destroyAllWindows()
```

