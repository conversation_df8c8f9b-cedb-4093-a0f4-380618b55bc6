import cv2
import time

def test_camera_1():
    """测试摄像头1的各种初始化方法"""
    print("开始测试摄像头1...")
    
    # 方法1：默认后端
    print("\n方法1：尝试默认后端")
    cap = cv2.VideoCapture(1)
    if cap.isOpened():
        print("✓ 默认后端打开成功")
        ret, frame = cap.read()
        if ret and frame is not None:
            print("✓ 默认后端读取图像成功")
            print(f"图像尺寸: {frame.shape}")
            cap.release()
            return True
        else:
            print("✗ 默认后端无法读取图像")
    else:
        print("✗ 默认后端无法打开")
    cap.release()
    
    # 方法2：MSMF后端
    print("\n方法2：尝试MSMF后端")
    cap = cv2.VideoCapture(1, cv2.CAP_MSMF)
    if cap.isOpened():
        print("✓ MSMF后端打开成功")
        ret, frame = cap.read()
        if ret and frame is not None:
            print("✓ MSMF后端读取图像成功")
            print(f"图像尺寸: {frame.shape}")
            cap.release()
            return True
        else:
            print("✗ MSMF后端无法读取图像")
    else:
        print("✗ MSMF后端无法打开")
    cap.release()
    
    # 方法3：DirectShow后端
    print("\n方法3：尝试DirectShow后端")
    cap = cv2.VideoCapture(1, cv2.CAP_DSHOW)
    if cap.isOpened():
        print("✓ DirectShow后端打开成功")
        ret, frame = cap.read()
        if ret and frame is not None:
            print("✓ DirectShow后端读取图像成功")
            print(f"图像尺寸: {frame.shape}")
            cap.release()
            return True
        else:
            print("✗ DirectShow后端无法读取图像")
    else:
        print("✗ DirectShow后端无法打开")
    cap.release()
    
    # 方法4：ANY后端
    print("\n方法4：尝试ANY后端")
    cap = cv2.VideoCapture(1, cv2.CAP_ANY)
    if cap.isOpened():
        print("✓ ANY后端打开成功")
        ret, frame = cap.read()
        if ret and frame is not None:
            print("✓ ANY后端读取图像成功")
            print(f"图像尺寸: {frame.shape}")
            cap.release()
            return True
        else:
            print("✗ ANY后端无法读取图像")
    else:
        print("✗ ANY后端无法打开")
    cap.release()
    
    print("\n所有方法都失败了")
    return False

def test_camera_0():
    """测试摄像头0作为对比"""
    print("\n\n对比测试摄像头0...")
    cap = cv2.VideoCapture(0)
    if cap.isOpened():
        print("✓ 摄像头0打开成功")
        ret, frame = cap.read()
        if ret and frame is not None:
            print("✓ 摄像头0读取图像成功")
            print(f"图像尺寸: {frame.shape}")
        else:
            print("✗ 摄像头0无法读取图像")
    else:
        print("✗ 摄像头0无法打开")
    cap.release()

if __name__ == "__main__":
    # 测试摄像头1
    success = test_camera_1()
    
    # 测试摄像头0作为对比
    test_camera_0()
    
    if success:
        print("\n✓ 摄像头1测试成功！")
    else:
        print("\n✗ 摄像头1测试失败")
    
    print("\n测试完成")
