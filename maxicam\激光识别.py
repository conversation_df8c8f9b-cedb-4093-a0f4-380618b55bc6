from maix import image, display, app, time, camera
import cv2
import numpy as np


def detect_lasers(img):
    Green_laser_coords = None
    Red_laser_coords = None

    # 转换颜色空间为 HSV
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

    # 定义激光点颜色范围
    lower_red1 = np.array([0, 100, 50])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 100, 50])
    upper_red2 = np.array([180, 255, 255])
    lower_green = np.array([40, 100, 50])
    upper_green = np.array([80, 255, 255])

    # 创建红色和绿色激光的二值化图像
    mask_red1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask_red2 = cv2.inRange(hsv, lower_red2, upper_red2)
    mask_red = cv2.bitwise_or(mask_red1, mask_red2)
    mask_green = cv2.inRange(hsv, lower_green, upper_green)

    # 闭运算
    kernel = np.ones((5, 5), np.uint8)
    mask_red = cv2.morphologyEx(mask_red, cv2.MORPH_CLOSE, kernel)
    mask_green = cv2.morphologyEx(mask_green, cv2.MORPH_CLOSE, kernel)

    # 寻找红色激光外轮廓
    contours_red, _ = cv2.findContours(mask_red, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in contours_red:
        # 找到最小矩形框
        rect = cv2.minAreaRect(contour)
        # 直接从元组中提取中心坐标
        laser_coords = tuple(map(int, rect[0]))
        r_sum, g_sum = get_pixel_sum(img, laser_coords)
        if r_sum > g_sum:
            Red_laser_coords = laser_coords
            cv2.circle(img, laser_coords, 5, (0, 0, 255), -1)
            cv2.putText(img, "Red Laser", (laser_coords[0], laser_coords[1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

    # 寻找绿色激光外轮廓
    contours_green, _ = cv2.findContours(mask_green, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in contours_green:
        # 找到最小矩形框
        rect = cv2.minAreaRect(contour)
        # 直接从元组中提取中心坐标
        laser_coords = tuple(map(int, rect[0]))
        r_sum, g_sum = get_pixel_sum(img, laser_coords)
        if g_sum > r_sum:
            Green_laser_coords = laser_coords
            cv2.circle(img, laser_coords, 5, (0, 255, 0), -1)
            cv2.putText(img, "Green Laser", (laser_coords[0], laser_coords[1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

    return Red_laser_coords, Green_laser_coords

    # 寻找绿色激光外轮廓
    contours_green, _ = cv2.findContours(mask_green, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in contours_green:
        # 找到最小矩形框
        rect = cv2.minAreaRect(contour)
        # 矩形框的中心坐标
        laser_coords = tuple(map(int, rect.center))
        r_sum, g_sum = get_pixel_sum(img, laser_coords)
        if g_sum > r_sum:
            Green_laser_coords = laser_coords
            cv2.circle(img, laser_coords, 5, (0, 255, 0), -1)
            cv2.putText(img, "Green Laser", (laser_coords[0], laser_coords[1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

    return Red_laser_coords, Green_laser_coords


def get_pixel_sum(image, coords):
    # 获取图像宽度和高度
    height, width = image.shape[:2]
    radius = 3
    # 确定方圆的左上角和右下角坐标
    x, y = coords
    x_start = max(0, x - radius)
    y_start = max(0, y - radius)
    x_end = min(width - 1, x + radius)
    y_end = min(height - 1, y + radius)

    # 提取方圆区域
    roi = image[y_start:y_end, x_start:x_end]

    # 计算 R 和 G 通道总值
    # 选取红色
    r_channel = roi[:, :, 2]
    # 绿色
    g_channel = roi[:, :, 1]
    # 求和
    r_sum = int(r_channel.sum())
    g_sum = int(g_channel.sum())

    return r_sum, g_sum


disp = display.Display()
cam = camera.Camera(320, 240, image.Format.FMT_BGR888)

while not app.need_exit():
    img = cam.read()
    # convert maix.image.Image object to numpy.ndarray object
    t = time.ticks_ms()
    img = image.image2cv(img, ensure_bgr=False, copy=False)
    print("time: ", time.ticks_ms() - t)

    # 检测激光
    detect_lasers(img)

    # show by maix.display
    img_show = image.cv2image(img, bgr=True, copy=False)
    disp.show(img_show)
    