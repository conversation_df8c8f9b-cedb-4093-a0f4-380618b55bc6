import cv2
import numpy as np
import os

# 全局参数，可根据需要修改
R_MIN = 0
R_MAX = 255
G_MIN = 0
G_MAX = 255
B_MIN = 0
B_MAX = 255
EXPOSURE = -7
ROI = [250, 185, 450, 320]  # x1, y1, x2, y2
INVERT = False

def load_parameters():
    """从阈值文件加载参数"""
    global R_MIN, R_MAX, G_MIN, G_MAX, B_MIN, B_MAX, EXPOSURE, ROI
    
    try:
        # 检查文件是否存在
        if os.path.exists("thresholds_RGB.txt"):
            with open("thresholds_RGB.txt", "r") as f:
                lines = f.readlines()
                # 读取RGB阈值
                rgb_lower = [int(x) for x in lines[0].strip().split(",")]
                rgb_upper = [int(x) for x in lines[1].strip().split(",")]
                
                R_MIN = rgb_lower[0]
                G_MIN = rgb_lower[1]
                B_MIN = rgb_lower[2]
                
                R_MAX = rgb_upper[0]
                G_MAX = rgb_upper[1]
                B_MAX = rgb_upper[2]
                
                # 读取ROI
                ROI = [int(x) for x in lines[2].strip().split(",")]
                
                # 读取曝光度
                if len(lines) > 3:
                    EXPOSURE = int(lines[3].strip())
                
                print(f"已从文件加载RGB参数: R({R_MIN}-{R_MAX}), G({G_MIN}-{G_MAX}), B({B_MIN}-{B_MAX})")
                print(f"ROI: {ROI}, 曝光度: {EXPOSURE}")
        else:
            print("阈值文件不存在，使用默认参数")
    except Exception as e:
        print(f"加载参数时出错: {str(e)}")
        print("使用默认参数")

def process_frame(frame):
    """处理一帧图像，应用RGB二值化处理"""
    # 防止空帧
    if frame is None or frame.size == 0:
        return np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 复制帧以免修改原始数据
    frame = frame.copy()
    orig_frame = frame.copy()
    
    # 应用ROI
    if ROI is not None:
        x1, y1, x2, y2 = ROI
        height, width = frame.shape[:2]
        
        # 确保坐标在图像范围内
        x1 = max(0, min(x1, width-1))
        y1 = max(0, min(y1, height-1))
        x2 = max(0, min(x2, width-1))
        y2 = max(0, min(y2, height-1))
        
        # 检查ROI是否有效
        if x2 > x1 and y2 > y1:
            # 提取ROI
            roi = frame[y1:y2, x1:x2]
            
            # 创建白色背景
            roi_display = np.ones_like(orig_frame) * 255
            
            # 调整ROI大小以填满窗口但保持比例
            roi_height, roi_width = roi.shape[:2]
            scale_factor = min(width / roi_width, height / roi_height)
            
            new_width = int(roi_width * scale_factor)
            new_height = int(roi_height * scale_factor)
            
            if new_width > 0 and new_height > 0:
                resized_roi = cv2.resize(roi, (new_width, new_height))
                
                # 计算居中坐标
                start_x = (width - new_width) // 2
                start_y = (height - new_height) // 2
                
                # 放置调整大小后的ROI
                roi_display[start_y:start_y+new_height, start_x:start_x+new_width] = resized_roi
                frame = roi_display
    
    # 应用RGB二值化
    b, g, r = cv2.split(frame)
    r_mask = cv2.inRange(r, R_MIN, R_MAX)
    g_mask = cv2.inRange(g, G_MIN, G_MAX)
    b_mask = cv2.inRange(b, B_MIN, B_MAX)
    
    # 合并掩码 - 使用AND操作
    mask = cv2.bitwise_and(r_mask, g_mask)
    mask = cv2.bitwise_and(mask, b_mask)
    
    result = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR)
    
    # 应用反转
    if INVERT:
        result = cv2.bitwise_not(result)
    
    return result

def main():
    # 加载参数
    load_parameters()
    
    # 打开摄像头
    cap = cv2.VideoCapture(0)
    
    # 设置曝光
    try:
        cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0)
        cap.set(cv2.CAP_PROP_EXPOSURE, EXPOSURE)
        print(f"摄像头曝光度设置为 {EXPOSURE}")
    except:
        print("设置曝光度失败，使用默认曝光")
    
    # 显示参数
    param_text = f"RGB模式 R:({R_MIN}-{R_MAX}) G:({G_MIN}-{G_MAX}) B:({B_MIN}-{B_MAX}) 曝光:{EXPOSURE}"
    print(param_text)
    
    while True:
        # 读取一帧
        ret, frame = cap.read()
        if not ret:
            print("无法读取摄像头")
            break
            
        # 处理图像
        result = process_frame(frame)
        
        # 显示原始图像和处理后的图像
        cv2.putText(frame, param_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        cv2.imshow("RGB模式 - 原始", frame)
        cv2.imshow("RGB模式 - 处理后", result)
        
        # 等待按键
        key = cv2.waitKey(30)
        if key == 27:  # ESC
            break
    
    # 释放资源
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main() 